import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { PaymentModule } from '../src/modules/payment/payment.module';
import { PaymentService } from '../src/modules/payment/services/payment.service';
import { KcpService } from '../src/modules/payment/services/kcp.service';
import { PaymentTransaction } from '../src/database/entities/payment-transaction.entity';
import { PaymentWebhook } from '../src/database/entities/payment-webhook.entity';
import { User } from '../src/database/entities/user.entity';
import { getTestDatabaseConfig } from './config/test-database.config';

describe('Payment Integration (e2e)', () => {
  let app: INestApplication;
  let paymentService: PaymentService;
  let kcpService: KcpService;
  let testUser: User;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRootAsync({
          useFactory: () => getTestDatabaseConfig(),
        }),
        TypeOrmModule.forFeature([
          PaymentTransaction,
          PaymentWebhook,
          User,
        ]),
        PaymentModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    paymentService = moduleFixture.get<PaymentService>(PaymentService);
    kcpService = moduleFixture.get<KcpService>(KcpService);

    await app.init();

    // Create test user
    testUser = await createTestUser();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Payment Initiation', () => {
    it('should initiate payment successfully', async () => {
      const paymentRequest = {
        orderId: `TEST-ORDER-${Date.now()}`,
        amount: 10000,
        currency: 'KRW',
        productName: 'Test Product',
        buyerName: 'Test User',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: 'card',
        purchaseType: 'shop_item',
        referenceId: 'test-reference-id',
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${await getTestToken(testUser)}`)
        .send(paymentRequest)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.transactionId).toBeDefined();
      expect(response.body.data.paymentUrl).toBeDefined();
    });

    it('should validate required fields', async () => {
      const invalidRequest = {
        orderId: '',
        amount: -100,
        // Missing required fields
      };

      await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${await getTestToken(testUser)}`)
        .send(invalidRequest)
        .expect(400);
    });
  });

  describe('Payment Status', () => {
    it('should get payment status', async () => {
      // First create a payment transaction
      const paymentRequest = {
        orderId: `TEST-ORDER-${Date.now()}`,
        amount: 10000,
        currency: 'KRW',
        productName: 'Test Product',
        buyerName: 'Test User',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: 'card',
        purchaseType: 'shop_item',
        referenceId: 'test-reference-id',
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const initResponse = await paymentService.initiatePayment(testUser.id, paymentRequest);
      const transactionId = initResponse.transactionId;

      const response = await request(app.getHttpServer())
        .get(`/payment/status/${transactionId}`)
        .set('Authorization', `Bearer ${await getTestToken(testUser)}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.transactionId).toBe(transactionId);
      expect(response.body.data.status).toBeDefined();
    });

    it('should return 404 for non-existent transaction', async () => {
      await request(app.getHttpServer())
        .get('/payment/status/non-existent-transaction')
        .set('Authorization', `Bearer ${await getTestToken(testUser)}`)
        .expect(404);
    });
  });

  describe('KCP Service', () => {
    it('should validate KCP configuration', () => {
      expect(kcpService).toBeDefined();
      // Add more KCP-specific tests here
    });

    it('should generate payment URL', async () => {
      const paymentRequest = {
        orderId: `TEST-ORDER-${Date.now()}`,
        amount: 10000,
        currency: 'KRW',
        productName: 'Test Product',
        buyerName: 'Test User',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: 'card',
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
        userId: testUser.id,
        purchaseType: 'shop_item',
        referenceId: 'test-reference-id',
      };

      const result = await kcpService.initiatePayment(paymentRequest);
      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
    });
  });

  describe('Webhook Processing', () => {
    it('should process webhook successfully', async () => {
      const webhookPayload = {
        site_cd: 'TEST_SITE',
        ordr_idxx: `TEST-ORDER-${Date.now()}`,
        tno: `TXN-${Date.now()}`,
        res_cd: '0000',
        res_msg: 'SUCCESS',
        amount: '10000',
        good_name: 'Test Product',
        buyr_name: 'Test User',
        buyr_mail: '<EMAIL>',
        pay_method: '100000000000',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/webhook/kcp')
        .set('x-kcp-signature', 'test-signature')
        .send(webhookPayload)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle payment initiation errors gracefully', async () => {
      const invalidPaymentRequest = {
        orderId: '',
        amount: 0,
        currency: 'INVALID',
        productName: '',
        buyerName: '',
        buyerEmail: 'invalid-email',
        buyerPhone: '',
        paymentMethod: 'invalid',
        purchaseType: 'invalid',
        referenceId: '',
        returnUrl: '',
        cancelUrl: '',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${await getTestToken(testUser)}`)
        .send(invalidPaymentRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBeDefined();
    });

    it('should handle unauthorized requests', async () => {
      const paymentRequest = {
        orderId: `TEST-ORDER-${Date.now()}`,
        amount: 10000,
        // ... other fields
      };

      await request(app.getHttpServer())
        .post('/payment/initiate')
        .send(paymentRequest)
        .expect(401);
    });
  });

  // Helper functions
  async function createTestUser(): Promise<User> {
    // Implementation to create a test user
    // This would typically use a test database
    return {
      id: 'test-user-id',
      email: '<EMAIL>',
      // ... other user properties
    } as User;
  }

  async function getTestToken(user: User): Promise<string> {
    // Implementation to generate a test JWT token
    // This would typically use the JWT service
    return 'test-jwt-token';
  }
});

describe('Payment Integration Unit Tests', () => {
  let paymentService: PaymentService;
  let kcpService: KcpService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        KcpService,
        // Mock dependencies
        {
          provide: 'PaymentTransactionRepository',
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: 'PaymentWebhookRepository',
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        // Add other mock providers as needed
      ],
    }).compile();

    paymentService = module.get<PaymentService>(PaymentService);
    kcpService = module.get<KcpService>(KcpService);
  });

  describe('PaymentService', () => {
    it('should be defined', () => {
      expect(paymentService).toBeDefined();
    });

    // Add more unit tests for PaymentService methods
  });

  describe('KcpService', () => {
    it('should be defined', () => {
      expect(kcpService).toBeDefined();
    });

    // Add more unit tests for KcpService methods
  });
});

import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { MissionDiaryEntry, MissionEntryStatus } from '../../database/entities/mission-diary-entry.entity';
import { DiaryMission } from '../../database/entities/diary-mission.entity';
import { MissionDiaryEntryFeedback } from '../../database/entities/mission-diary-entry-feedback.entity';
import { User, UserType } from '../../database/entities/user.entity';
import {
  CreateMissionDiaryEntryDto,
  UpdateMissionDiaryEntryDto,
  MissionDiaryEntryResponseDto,
  MissionEntryFilterDto,
  AddMissionFeedbackDto,
  AddMissionCorrectionDto,
  AssignMissionScoreDto,
  AddMissionCorrectionWithScoreDto,
  MissionFeedbackResponseDto
} from '../../database/models/mission-diary-entry.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { DiaryMissionService } from './diary-mission.service';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';

@Injectable()
export class MissionDiaryEntryService {
  private readonly logger = new Logger(MissionDiaryEntryService.name);

  constructor(
    @InjectRepository(MissionDiaryEntry)
    private readonly missionDiaryEntryRepository: Repository<MissionDiaryEntry>,
    @InjectRepository(DiaryMission)
    private readonly diaryMissionRepository: Repository<DiaryMission>,
    @InjectRepository(MissionDiaryEntryFeedback)
    private readonly feedbackRepository: Repository<MissionDiaryEntryFeedback>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly notificationHelper: NotificationHelperService,
    private readonly tutorMatchingService: TutorMatchingService,
    private readonly diaryMissionService: DiaryMissionService,
    private readonly deeplinkService: DeeplinkService
  ) {}

  /**
   * Create a new mission diary entry
   * @param studentId ID of the student creating the entry
   * @param createDto Entry creation data
   * @returns The created entry
   */
  async createMissionEntry(studentId: string, createDto: CreateMissionDiaryEntryDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the student exists
      const student = await this.userRepository.findOne({ where: { id: studentId } });
      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      if (student.type !== UserType.STUDENT) {
        throw new BadRequestException('Only students can create mission entries');
      }

      // Verify the mission exists and is active
      const mission = await this.diaryMissionRepository.findOne({
        where: { id: createDto.missionId },
        relations: ['tutor']
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${createDto.missionId} not found`);
      }

      if (!mission.isActive) {
        throw new BadRequestException('This mission is not active');
      }

      // Check if the mission has expired
      const now = getCurrentUTCDate();
      if (mission.expiryDate && mission.expiryDate < now) {
        throw new BadRequestException('This mission has expired');
      }

      // Check if the student already has an entry for this mission
      const existingEntry = await this.missionDiaryEntryRepository.findOne({
        where: {
          missionId: createDto.missionId,
          studentId
        }
      });

      if (existingEntry) {
        throw new BadRequestException('You already have an entry for this mission');
      }

      // Validate word count - only check max word count for create
      const wordCount = this.calculateWordCount(createDto.content);
      if (mission.targetMaxWordCount && wordCount > mission.targetMaxWordCount) {
        throw new BadRequestException(`Entry cannot exceed ${mission.targetMaxWordCount} words`);
      }

      const progress = this.calculateProgress(wordCount, mission.targetWordCount);

      // Create the entry
      const entry = this.missionDiaryEntryRepository.create({
        missionId: createDto.missionId,
        studentId,
        content: createDto.content,
        wordCount,
        progress,
        status: MissionEntryStatus.NEW
      });

      // Save the entry
      const savedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Created mission entry with ID ${savedEntry.id} by student ${studentId}`);

      return this.mapToResponseDto(savedEntry, mission, student);
    } catch (error) {
      this.logger.error(`Error creating mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update an existing mission diary entry
   * @param id Entry ID
   * @param studentId ID of the student updating the entry
   * @param updateDto Entry update data
   * @returns The updated entry
   */
  async updateMissionEntry(id: string, studentId: string, updateDto: UpdateMissionDiaryEntryDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id },
        relations: ['mission', 'mission.tutor', 'student']
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${id} not found`);
      }

      // Check if the student is the creator of the entry
      if (entry.studentId !== studentId) {
        throw new ForbiddenException('You can only update your own entries');
      }

      // Allow unlimited updates - students can update entries in any status
      // Tutors will be notified of updates after submission

      // Update the entry
      if (updateDto.content !== undefined) {
        // Validate word count using the submitted content - only check max word count for update
        const wordCount = this.calculateWordCount(updateDto.content);

        if (entry.mission.targetMaxWordCount && wordCount > entry.mission.targetMaxWordCount) {
          throw new BadRequestException(`Entry cannot exceed ${entry.mission.targetMaxWordCount} words`);
        }

        // Update content, word count and progress
        const previousWordCount = entry.wordCount;
        entry.content = updateDto.content;
        entry.wordCount = wordCount;
        entry.progress = this.calculateProgress(wordCount, entry.mission.targetWordCount);

        // Check if the entry now meets the target word count while previously it didn't
        const meetsTargetNow = wordCount >= entry.mission.targetWordCount;
        const metTargetBefore = previousWordCount >= entry.mission.targetWordCount;

        if (meetsTargetNow && !metTargetBefore) { // Send notification when target word count is met for the first time
          // Get the tutor assigned to this student
          const tutorMappingsResult = await this.tutorMatchingService.getStudentTutors(studentId);

          if (tutorMappingsResult?.items?.length > 0) {
            const tutorId = tutorMappingsResult.items[0].id;
            const tutor = await this.userRepository.findOne({ where: { id: tutorId } });

            if (tutor) {
              await this.sendMissionEntryNotification(
                studentId,
                entry.id,
                entry,
                tutor,
                NotificationType.MISSION_SUBMISSION_UPDATED,
                'Mission Entry Update - Target Word Count Met',
                `${entry.student.name} has met the target word count for mission: "${entry.mission.title}"`,
                'View Entry'
              );
            }
          }
        }
      }

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Updated mission entry with ID ${updatedEntry.id} by student ${studentId}`);

      // If entry was already submitted/reviewed/confirmed, notify tutor of the update
      if (entry.status === MissionEntryStatus.SUBMITTED || entry.status === MissionEntryStatus.REVIEWED ||
          entry.status === MissionEntryStatus.CONFIRMED || entry.status === MissionEntryStatus.LEGACY_SUBMIT ||
          entry.status === MissionEntryStatus.LEGACY_REVIEWED || entry.status === MissionEntryStatus.LEGACY_CONFIRM) {
        try {
          // Get the tutor assigned to this student
          const tutorMappingsResult = await this.tutorMatchingService.getStudentTutors(studentId);

          if (tutorMappingsResult?.items?.length > 0) {
            const tutorId = tutorMappingsResult.items[0].id;
            const tutor = await this.userRepository.findOne({ where: { id: tutorId } });

            if (tutor) {
              await this.sendMissionEntryNotification(
                studentId,
                entry.id,
                entry,
                tutor,
                NotificationType.MISSION_SUBMISSION_UPDATED,
                'Mission Entry Updated After Submission',
                `${entry.student.name} has updated their mission entry after submission: "${entry.mission.title}"`,
                'Review Updated Entry'
              );
            }
          }
        } catch (error) {
          this.logger.warn(`Failed to send update notification: ${error.message}`);
        }
      }

      return this.mapToResponseDto(updatedEntry, entry.mission, entry.student);
    } catch (error) {
      this.logger.error(`Error updating mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get or create a mission diary entry by mission ID
   * @param missionId Mission ID
   * @param studentId Student ID
   * @returns The entry (existing or new)
   */
  async getOrCreateMissionEntry(missionId: string, studentId: string): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the student exists
      const student = await this.userRepository.findOne({ where: { id: studentId } });
      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      if (student.type !== UserType.STUDENT) {
        throw new BadRequestException('Only students can access mission entries');
      }

      // Verify the mission exists and is active
      const mission = await this.diaryMissionRepository.findOne({
        where: { id: missionId },
        relations: ['tutor']
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${missionId} not found`);
      }

      if (!mission.isActive) {
        throw new BadRequestException('This mission is not active');
      }

      // Check if the mission has expired
      const now = getCurrentUTCDate();
      if (mission.expiryDate && mission.expiryDate < now) {
        throw new BadRequestException('This mission has expired');
      }

      // Check if the student already has an entry for this mission
      let entry = await this.missionDiaryEntryRepository.findOne({
        where: {
          missionId,
          studentId
        },
        relations: [
          'mission',
          'mission.tutor',
          'student',
          'reviewer',
          'feedbacks',
          'feedbacks.tutor'
        ]
      });

      if (entry) {
        return this.mapToResponseDto(entry, mission, student);
      }

      // Create a new entry with empty content
      entry = this.missionDiaryEntryRepository.create({
        missionId,
        studentId,
        content: '',
        wordCount: 0,
        progress: 0,
        status: MissionEntryStatus.NEW
      });

      // Save the entry
      const savedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Created empty mission entry with ID ${savedEntry.id} by student ${studentId}`);

      return this.mapToResponseDto(savedEntry, mission, student);
    } catch (error) {
      this.logger.error(`Error getting or creating mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific mission diary entry by ID
   * @param id Entry ID
   * @returns The entry
   */
  async getMissionEntry(id: string): Promise<MissionDiaryEntryResponseDto> {
    try {
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id },
        relations: [
          'mission',
          'mission.tutor',
          'student',
          'reviewer',
          'feedbacks',
          'feedbacks.tutor'
        ]
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${id} not found`);
      }

      return this.mapToResponseDto(entry, entry.mission, entry.student, entry.reviewer);
    } catch (error) {
      this.logger.error(`Error getting mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get mission entries for a specific student with filtering and pagination
   * @param studentId Student ID
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of entries
   */
  async getStudentMissionEntries(
    studentId: string,
    filterDto: MissionEntryFilterDto = {},
    paginationDto: PaginationDto = { page: 1, limit: 10 }
  ): Promise<PagedListDto<MissionDiaryEntryResponseDto>> {
    try {
      const { page = 1, limit = 10 } = paginationDto;
      const skip = (page - 1) * limit;

      // Build query
      const queryBuilder = this.missionDiaryEntryRepository.createQueryBuilder('entry')
        .leftJoinAndSelect('entry.mission', 'mission')
        .leftJoinAndSelect('mission.tutor', 'tutor')
        .leftJoinAndSelect('entry.student', 'student')
        .leftJoinAndSelect('entry.reviewer', 'reviewer')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
        .where('entry.studentId = :studentId', { studentId })
        .orderBy('entry.createdAt', 'DESC');

      // Apply filters
      if (filterDto.missionId) {
        queryBuilder.andWhere('entry.missionId = :missionId', { missionId: filterDto.missionId });
      }

      if (filterDto.status) {
        queryBuilder.andWhere('entry.status = :status', { status: filterDto.status });
      }

      if (filterDto.createdAtFrom) {
        queryBuilder.andWhere('entry.createdAt >= :createdAtFrom', { createdAtFrom: new Date(filterDto.createdAtFrom) });
      }

      if (filterDto.createdAtTo) {
        queryBuilder.andWhere('entry.createdAt <= :createdAtTo', { createdAtTo: new Date(filterDto.createdAtTo) });
      }

      // Get total count
      const totalItems = await queryBuilder.getCount();

      // Get paginated results
      const entries = await queryBuilder
        .skip(skip)
        .take(limit)
        .getMany();

      // Map to response DTOs
      const items = entries.map(entry =>
        this.mapToResponseDto(entry, entry.mission, entry.student, entry.reviewer)
      );

      return new PagedListDto(
        items,
        totalItems,
        page,
        limit
      );
    } catch (error) {
      this.logger.error(`Error getting student mission entries: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get mission entries for a specific tutor with filtering and pagination
   * @param tutorId Tutor ID
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of entries
   */
  async getTutorMissionEntries(
    tutorId: string,
    filterDto: MissionEntryFilterDto = {},
    paginationDto: PaginationDto = { page: 1, limit: 10 }
  ): Promise<PagedListDto<MissionDiaryEntryResponseDto>> {
    try {
      const { page = 1, limit = 10 } = paginationDto;
      const skip = (page - 1) * limit;

      // Get the tutor's assigned students
      const studentMappings = await this.tutorMatchingService.getTutorStudents(tutorId);
      const studentIds = studentMappings.map((mapping: any) => mapping.id);

      if (studentIds.length === 0) {
        // Return empty result if no students assigned
        return new PagedListDto(
          [],
          0,
          page,
          limit
        );
      }

      // Build query
      const queryBuilder = this.missionDiaryEntryRepository.createQueryBuilder('entry')
        .leftJoinAndSelect('entry.mission', 'mission')
        .leftJoinAndSelect('mission.tutor', 'tutor')
        .leftJoinAndSelect('entry.student', 'student')
        .leftJoinAndSelect('entry.reviewer', 'reviewer')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
        .where('entry.studentId IN (:...studentIds)', { studentIds })
        .orderBy('entry.createdAt', 'DESC');

      // Apply filters
      if (filterDto.missionId) {
        queryBuilder.andWhere('entry.missionId = :missionId', { missionId: filterDto.missionId });
      }

      if (filterDto.studentId) {
        queryBuilder.andWhere('entry.studentId = :studentId', { studentId: filterDto.studentId });
      }

      if (filterDto.status) {
        queryBuilder.andWhere('entry.status = :status', { status: filterDto.status });
      }

      if (filterDto.createdAtFrom) {
        queryBuilder.andWhere('entry.createdAt >= :createdAtFrom', { createdAtFrom: new Date(filterDto.createdAtFrom) });
      }

      if (filterDto.createdAtTo) {
        queryBuilder.andWhere('entry.createdAt <= :createdAtTo', { createdAtTo: new Date(filterDto.createdAtTo) });
      }

      // Get total count
      const totalItems = await queryBuilder.getCount();

      // Get paginated results
      const entries = await queryBuilder
        .skip(skip)
        .take(limit)
        .getMany();

      // Map to response DTOs
      const items = entries.map(entry =>
        this.mapToResponseDto(entry, entry.mission, entry.student, entry.reviewer)
      );

      return new PagedListDto(
        items,
        totalItems,
        page,
        limit
      );
    } catch (error) {
      this.logger.error(`Error getting tutor mission entries: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Submit a mission diary entry for review
   * @param id Entry ID
   * @param studentId ID of the student submitting the entry
   * @returns The submitted entry
   */
  async submitMissionEntry(id: string, studentId: string, submitDto?: UpdateMissionDiaryEntryDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id },
        relations: ['mission', 'mission.tutor', 'student']
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${id} not found`);
      }

      // Check if the student is the creator of the entry
      if (entry.studentId !== studentId) {
        throw new ForbiddenException('You can only submit your own entries');
      }

      // Check if the entry is in a state that can be submitted
      if (entry.status !== MissionEntryStatus.NEW && entry.status !== MissionEntryStatus.LEGACY_NEW) {
        throw new BadRequestException('This entry has already been submitted');
      }

      // Check word count requirements using submitted content
      const wordCount = this.calculateWordCount(submitDto.content);
      if (wordCount < entry.mission.targetWordCount) {
        throw new BadRequestException(`Entry must contain at least ${entry.mission.targetWordCount} words before submission`);
      }

      if (entry.mission.targetMaxWordCount && wordCount > entry.mission.targetMaxWordCount) {
        throw new BadRequestException(`Entry cannot exceed ${entry.mission.targetMaxWordCount} words`);
      }

      // Update the entry content and word count
      entry.content = submitDto.content;
      entry.wordCount = wordCount;
      entry.progress = this.calculateProgress(wordCount, entry.mission.targetWordCount);

      // Update the entry status
      entry.status = MissionEntryStatus.SUBMITTED;

      // Save the updated entry
      const submittedEntry = await this.missionDiaryEntryRepository.save(entry);

      // Get the tutor assigned to this student
      const tutorMappingsResult = await this.tutorMatchingService.getStudentTutors(studentId);

      if (tutorMappingsResult?.items?.length > 0) {
        const tutorId = tutorMappingsResult.items[0].id;
        const tutor = await this.userRepository.findOne({ where: { id: tutorId } });

        if (tutor) {
          await this.sendMissionEntryNotification(
            studentId,
            entry.id,
            entry,
            tutor,
            NotificationType.MISSION_SUBMISSION,
            'New Mission Entry Submission',
            `${entry.student.name} has submitted an entry for mission: "${entry.mission.title}"`,
            'Review Submission'
          );
        }
      }

      return this.mapToResponseDto(submittedEntry, entry.mission, entry.student);
    } catch (error) {
      this.logger.error(`Error submitting mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add feedback to a mission diary entry
   * @param entryId Entry ID
   * @param tutorId ID of the tutor adding the feedback
   * @param feedbackDto Feedback data
   * @returns The added feedback
   */
  async addFeedback(entryId: string, tutorId: string, feedbackDto: AddMissionFeedbackDto): Promise<MissionFeedbackResponseDto> {
    try {
      // Verify the tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException('Only tutors can add feedback');
      }

      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.tutor', 'student']
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can receive feedback
      if (entry.status === MissionEntryStatus.NEW || entry.status === MissionEntryStatus.LEGACY_NEW) {
        throw new BadRequestException('Cannot add feedback to an entry that has not been submitted');
      }

      // Create the feedback
      const feedback = this.feedbackRepository.create({
        missionEntryId: entryId,
        tutorId,
        feedback: feedbackDto.feedback,
        rating: feedbackDto.rating
      });

      // Save the feedback
      const savedFeedback = await this.feedbackRepository.save(feedback);
      this.logger.log(`Added feedback with ID ${savedFeedback.id} to mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student
      try {
        // Generate deeplinks for the mission entry
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entryId,
          linkText: 'View Feedback',
          buttonStyle: true
        });

        // Create HTML content for email notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">New Feedback on Your Mission Entry</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student.name || 'there'},</p>
              <p>Your tutor ${tutor.name} has provided feedback on your mission entry:</p>
              <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <p><strong>Mission:</strong> ${entry.mission.title}</p>
                <p><strong>Feedback:</strong> ${feedbackDto.feedback}</p>
                ${feedbackDto.rating ? `<p><strong>Rating:</strong> ${feedbackDto.rating}/5</p>` : ''}
              </div>
              <p>Click the button below to view the complete feedback:</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.notificationHelper.notify(
          entry.studentId,
          NotificationType.MISSION_FEEDBACK,
          'New Feedback on Your Mission Entry',
          `${tutor.name} has provided feedback on your mission entry: "${entry.mission.title}"`,
          {
            relatedEntityId: entryId,
            relatedEntityType: 'mission_diary_entry',
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      } catch (error) {
        this.logger.warn(`Failed to send notification: ${error.message}`);
      }

      return {
        id: savedFeedback.id,
        missionEntryId: savedFeedback.missionEntryId,
        tutorId: savedFeedback.tutorId,
        tutorName: tutor.name,
        feedback: savedFeedback.feedback,
        rating: savedFeedback.rating,
        createdAt: savedFeedback.createdAt
      };
    } catch (error) {
      this.logger.error(`Error adding feedback: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add correction to a mission diary entry
   * @param entryId Entry ID
   * @param tutorId ID of the tutor adding the correction
   * @param correctionDto Correction data
   * @returns The updated entry
   */
  async addCorrection(entryId: string, tutorId: string, correctionDto: AddMissionCorrectionDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException('Only tutors can add corrections');
      }

      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.tutor', 'student', 'feedbacks', 'feedbacks.tutor']
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can receive corrections
      if (entry.status === MissionEntryStatus.NEW || entry.status === MissionEntryStatus.LEGACY_NEW) {
        throw new BadRequestException('Cannot add correction to an entry that has not been submitted');
      }

      // Allow multiple corrections - tutors can update corrections unlimited times

      // Update the entry with the correction
      entry.correction = correctionDto.correction;
      entry.correctionProvidedAt = getCurrentUTCDate();
      entry.correctionProvidedBy = tutorId;
      entry.status = MissionEntryStatus.REVIEWED;

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Added correction to mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student
      try {
        // Generate deeplinks for the mission entry
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entryId,
          linkText: 'View Corrections',
          buttonStyle: true
        });

        // Create HTML content for email notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">Corrections on Your Mission Entry</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student.name || 'there'},</p>
              <p>Your tutor ${tutor.name} has provided corrections on your mission entry:</p>
              <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <p><strong>Mission:</strong> ${entry.mission.title}</p>
                <p><strong>Corrections:</strong> ${correctionDto.correction}</p>
              </div>
              <p>Click the button below to view the complete corrections:</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.notificationHelper.notify(
          entry.studentId,
          NotificationType.MISSION_CORRECTION,
          'Corrections on Your Mission Entry',
          `${tutor.name} has provided corrections on your mission entry: "${entry.mission.title}"`,
          {
            relatedEntityId: entryId,
            relatedEntityType: 'mission_diary_entry',
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      } catch (error) {
        this.logger.warn(`Failed to send notification: ${error.message}`);
      }

      return this.mapToResponseDto(updatedEntry, entry.mission, entry.student, tutor);
    } catch (error) {
      this.logger.error(`Error adding correction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Assign a score to a mission diary entry
   * @param entryId Entry ID
   * @param tutorId ID of the tutor assigning the score
   * @param scoreDto Score data
   * @returns The updated entry
   */
  async assignScore(entryId: string, tutorId: string, scoreDto: AssignMissionScoreDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException('Only tutors can assign scores');
      }

      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.tutor', 'student', 'feedbacks', 'feedbacks.tutor']
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can receive a score
      if (entry.status === MissionEntryStatus.NEW || entry.status === MissionEntryStatus.LEGACY_NEW) {
        throw new BadRequestException('Cannot assign a score to an entry that has not been submitted');
      }

      // Check if a score has already been assigned
      if (entry.gainedScore !== null && entry.gainedScore !== undefined) {
        throw new BadRequestException('A score has already been assigned to this entry');
      }

      // Validate the score
      if (scoreDto.score < 0 || scoreDto.score > entry.mission.score) {
        throw new BadRequestException(`Score must be between 0 and ${entry.mission.score}`);
      }

      // Update the entry with the score
      entry.gainedScore = scoreDto.score;
      entry.reviewedBy = tutorId;
      entry.reviewedAt = getCurrentUTCDate();
      entry.status = MissionEntryStatus.REVIEWED;

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Assigned score ${scoreDto.score} to mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student
      try {
        // Generate deeplinks for the mission entry
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entryId,
          linkText: 'View Review',
          buttonStyle: true
        });

        // Create HTML content for email notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">Mission Entry Review Completed</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student.name || 'there'},</p>
              <p>Your tutor ${tutor.name} has completed the review of your mission entry:</p>
              <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <p><strong>Mission:</strong> ${entry.mission.title}</p>
                <p><strong>Score:</strong> ${scoreDto.score}/${entry.mission.score}</p>
                <p><strong>Percentage:</strong> ${Math.round((scoreDto.score / entry.mission.score) * 100)}%</p>
              </div>
              <p>Click the button below to view the complete review:</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.notificationHelper.notify(
          entry.studentId,
          NotificationType.MISSION_REVIEW_COMPLETE,
          'Mission Entry Review Completed',
          `${tutor.name} has completed the review of your mission entry: "${entry.mission.title}"`,
          {
            relatedEntityId: entryId,
            relatedEntityType: 'mission_diary_entry',
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      } catch (error) {
        this.logger.warn(`Failed to send notification: ${error.message}`);
      }

      return this.mapToResponseDto(updatedEntry, entry.mission, entry.student, tutor);
    } catch (error) {
      this.logger.error(`Error assigning score: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add correction with score to a mission diary entry in a single operation
   * @param entryId Entry ID
   * @param tutorId ID of the tutor adding the correction and score
   * @param correctionWithScoreDto Correction and score data
   * @returns The updated entry
   */
  async addCorrectionWithScore(entryId: string, tutorId: string, correctionWithScoreDto: AddMissionCorrectionWithScoreDto): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException('Only tutors can add corrections and scores');
      }

      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.tutor', 'student', 'feedbacks', 'feedbacks.tutor']
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can receive corrections and scores
      if (entry.status === MissionEntryStatus.NEW || entry.status === MissionEntryStatus.LEGACY_NEW) {
        throw new BadRequestException('Cannot add correction and score to an entry that has not been submitted');
      }

      // Allow multiple corrections - tutors can update corrections unlimited times

      // Check if a score has already been assigned
      if (entry.gainedScore !== null && entry.gainedScore !== undefined) {
        throw new BadRequestException('A score has already been assigned to this entry');
      }

      // Validate the score
      if (correctionWithScoreDto.score < 0 || correctionWithScoreDto.score > entry.mission.score) {
        throw new BadRequestException(`Score must be between 0 and ${entry.mission.score}`);
      }

      // Update the entry with both correction and score
      entry.correction = correctionWithScoreDto.correction;
      entry.correctionProvidedAt = getCurrentUTCDate();
      entry.correctionProvidedBy = tutorId;
      entry.gainedScore = correctionWithScoreDto.score;
      entry.reviewedBy = tutorId;
      entry.reviewedAt = getCurrentUTCDate();
      entry.status = MissionEntryStatus.REVIEWED;

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Added correction and assigned score ${correctionWithScoreDto.score} to mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student about the correction and score
      try {
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entryId,
          linkText: 'View Your Entry',
          buttonStyle: true
        });

        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">Mission Entry Review Completed</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student?.name || 'there'},</p>
              <p>${tutor.name} has completed the review of your mission entry: "${entry.mission.title}"</p>
              <p><strong>Score:</strong> ${correctionWithScoreDto.score}/${entry.mission.score}</p>
              <p><strong>Corrections:</strong> ${correctionWithScoreDto.correction}</p>
              <p>Click the button below to view your entry:</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.notificationHelper.notify(
          entry.studentId,
          NotificationType.MISSION_REVIEW_COMPLETE,
          'Mission Entry Review Completed',
          `${tutor.name} has completed the review of your mission entry: "${entry.mission.title}" with score ${correctionWithScoreDto.score}/${entry.mission.score}`,
          {
            relatedEntityId: entryId,
            relatedEntityType: 'mission_diary_entry',
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      } catch (error) {
        this.logger.warn(`Failed to send notification: ${error.message}`);
      }

      return this.mapToResponseDto(updatedEntry, entry.mission, entry.student, tutor);
    } catch (error) {
      this.logger.error(`Error adding correction with score: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Confirm a mission diary entry review
   * @param entryId Entry ID
   * @param tutorId ID of the tutor confirming the review
   * @returns The updated entry
   */
  async confirmMissionEntry(entryId: string, tutorId: string): Promise<MissionDiaryEntryResponseDto> {
    try {
      // Verify the tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      if (tutor.type !== UserType.TUTOR) {
        throw new BadRequestException('Only tutors can confirm mission entries');
      }

      // Find the entry
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['mission', 'mission.tutor', 'student', 'feedbacks', 'feedbacks.tutor']
      });

      if (!entry) {
        throw new NotFoundException(`Mission entry with ID ${entryId} not found`);
      }

      // Check if the entry is in a state that can be confirmed
      if (entry.status !== MissionEntryStatus.REVIEWED && entry.status !== MissionEntryStatus.LEGACY_REVIEWED) {
        throw new BadRequestException('Can only confirm entries that have been reviewed');
      }

      // Check if a score has been assigned
      if (entry.gainedScore === null || entry.gainedScore === undefined) {
        throw new BadRequestException('Cannot confirm an entry without a score');
      }

      // Update the entry status to confirmed
      entry.status = MissionEntryStatus.CONFIRMED;

      // Save the updated entry
      const updatedEntry = await this.missionDiaryEntryRepository.save(entry);
      this.logger.log(`Confirmed mission entry ${entryId} by tutor ${tutorId}`);

      // Send notification to student about confirmation
      try {
        const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
        const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
          id: entryId,
          linkText: 'View Confirmed Entry',
          buttonStyle: true
        });

        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">Mission Entry Confirmed</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.student?.name || 'there'},</p>
              <p>${tutor.name} has confirmed your mission entry: "${entry.mission.title}"</p>
              <p><strong>Final Score:</strong> ${entry.gainedScore}/${entry.mission.score}</p>
              <p>Your entry has been successfully completed and confirmed.</p>
              <div style="text-align: center; margin: 25px 0;">
                ${entryButton}
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.notificationHelper.notify(
          entry.studentId,
          NotificationType.MISSION_CONFIRMED,
          'Mission Entry Confirmed',
          `${tutor.name} has confirmed your mission entry: "${entry.mission.title}"`,
          {
            relatedEntityId: entryId,
            relatedEntityType: 'mission_diary_entry',
            htmlContent: htmlContent,
            webLink: webLink,
            deepLink: deepLink,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      } catch (error) {
        this.logger.warn(`Failed to send confirmation notification: ${error.message}`);
      }

      return this.mapToResponseDto(updatedEntry, entry.mission, entry.student, tutor);
    } catch (error) {
      this.logger.error(`Error confirming mission entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate the word count of a text
   * @param content The text content
   * @returns The word count
   */
  calculateWordCount(content: string): number {
    // Handle empty or null content
    if (!content) return 0;

    // Normalize content: trim, replace multiple spaces with single space
    const normalizedContent = content.trim().replace(/\s+/g, ' ');

    // Handle case where content is just whitespace
    if (normalizedContent === '') return 0;

    // Split by spaces and count words
    return normalizedContent.split(' ').length;
  }

  /**
   * Calculate the progress percentage based on word count
   * @param wordCount The current word count
   * @param targetWordCount The target word count
   * @returns The progress percentage (0-100)
   */
  calculateProgress(wordCount: number, targetWordCount: number): number {
    // Handle invalid inputs
    if (targetWordCount <= 0) return 0;
    if (wordCount < 0) return 0;

    // Calculate progress percentage
    const progress = (wordCount / targetWordCount) * 100;

    // Ensure progress is between 0 and 100
    return Math.max(0, Math.min(progress, 100));
  }

  /**
   * Map a MissionDiaryEntry entity to a MissionDiaryEntryResponseDto
   * @param entry The entry entity
   * @param mission The mission entity (optional)
   * @param student The student user entity (optional)
   * @param reviewer The reviewer user entity (optional)
   * @returns The mapped DTO
   */
  private mapToResponseDto(
    entry: MissionDiaryEntry,
    mission?: DiaryMission,
    student?: User,
    reviewer?: User
  ): MissionDiaryEntryResponseDto {
    // Map feedbacks if available
    const feedbacks = entry.feedbacks?.map(feedback => ({
      id: feedback.id,
      missionEntryId: feedback.missionEntryId,
      tutorId: feedback.tutorId,
      tutorName: feedback.tutor?.name,
      feedback: feedback.feedback,
      rating: feedback.rating,
      createdAt: feedback.createdAt
    })) || [];

    // Map mission if available
    const missionDto = mission ? {
      id: mission.id,
      title: mission.title,
      description: mission.description,
      targetWordCount: mission.targetWordCount,
      targetMaxWordCount: mission.targetMaxWordCount,
      publishDate: mission.publishDate,
      expiryDate: mission.expiryDate,
      adminId: mission.adminId,
      createdBy: mission.adminId,
      isActive: mission.isActive,
      score: mission.score,
      createdAt: mission.createdAt,
      updatedAt: mission.updatedAt
    } : undefined;

    return {
      id: entry.id,
      missionId: entry.missionId,
      mission: missionDto,
      studentId: entry.studentId,
      studentName: student?.name,
      content: entry.content,
      wordCount: entry.wordCount,
      progress: entry.progress,
      status: entry.status,
      // Include feedback and correction information
      feedbacks: feedbacks,
      gainedScore: entry.gainedScore,
      correction: entry.correction,
      correctionProvidedAt: entry.correctionProvidedAt,
      correctionProvidedBy: entry.correctionProvidedBy,
      reviewedBy: entry.reviewedBy || reviewer?.id,
      reviewerName: reviewer?.name,
      reviewedAt: entry.reviewedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt
    };
  }

  /**
   * Send notification about a mission diary entry
   * @param studentId Student ID
   * @param entryId Entry ID
   * @param entry The mission diary entry
   * @param tutor The tutor user
   * @param type The type of notification (submission, update, etc.)
   * @param title The notification title
   * @param description The notification description
   * @param linkText The text to show on the action button
   * @returns Promise<void>
   */
  private async sendMissionEntryNotification(
    studentId: string,
    entryId: string,
    entry: MissionDiaryEntry,
    tutor: User,
    notificationType: NotificationType,
    title: string,
    description: string,
    linkText: string
  ): Promise<void> {
    try {
      // Generate deeplinks for the mission entry
      const webLink = this.deeplinkService.getWebLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
      const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.MISSION_DIARY_ENTRY, { id: entryId });
      const entryButton = this.deeplinkService.getLinkHtml(DeeplinkType.MISSION_DIARY_ENTRY, {
        id: entryId,
        linkText,
        buttonStyle: true
      });

      // Create HTML content for email notification
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #333;">${title}</h2>
          </div>
          <div style="margin-bottom: 20px;">
            <p>Hello ${tutor.name || 'there'},</p>
            <p>${description}</p>
            <p><strong>Word Count:</strong> ${entry.wordCount} (${entry.progress.toFixed(0)}% of target)</p>
            <p>Click the button below to view the entry:</p>
            <div style="text-align: center; margin: 25px 0;">
              ${entryButton}
            </div>
          </div>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
            <p>This is an automated message from the HEC system.</p>
            <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
          </div>
        </div>
      `;

      await this.notificationHelper.notify(
        tutor.id,
        notificationType,
        title,
        description,
        {
          relatedEntityId: entryId,
          relatedEntityType: 'mission_diary_entry',
          htmlContent: htmlContent,
          webLink: webLink,
          deepLink: deepLink,
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false
        }
      );
    } catch (error) {
      this.logger.warn(`Failed to send notification: ${error.message}`);
    }
  }
}

import { Injectable, NotFoundException, BadRequestException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { NovelEntry, NovelEntryStatus } from '../../../database/entities/novel-entry.entity';
import { NovelFeedback } from '../../../database/entities/novel-feedback.entity';
import { NovelCorrection } from '../../../database/entities/novel-correction.entity';
import { StudentTutorMapping, MappingStatus } from '../../../database/entities/student-tutor-mapping.entity';
import {
  CreateNovelFeedbackDto,
  CreateNovelCorrectionDto,
  UpdateNovelCorrectionDto,
  CreateNovelReviewDto,
  NovelFeedbackResponseDto,
  NovelCorrectionResponseDto,
  NovelEntryResponseDto
} from '../../../database/models/novel.dto';
import { NotificationHelperService } from '../../notification/notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';

@Injectable()
export class NovelReviewService {
  private readonly logger = new Logger(NovelReviewService.name);

  constructor(
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
    @InjectRepository(NovelFeedback)
    private readonly novelFeedbackRepository: Repository<NovelFeedback>,
    @InjectRepository(NovelCorrection)
    private readonly novelCorrectionRepository: Repository<NovelCorrection>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => NotificationHelperService))
    private readonly notificationHelper: NotificationHelperService
  ) {}

  async getTutorEntries(tutorId: string): Promise<NovelEntryResponseDto[]> {
    // Get students assigned to this tutor
    const tutorMappings = await this.studentTutorMappingRepository.find({
      where: { tutorId, status: MappingStatus.ACTIVE },
      select: ['studentId']
    });

    const studentIds = tutorMappings.map(mapping => mapping.studentId);

    if (studentIds.length === 0) {
      return [];
    }

    const entries = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .leftJoinAndSelect('entry.topic', 'topic')
      .leftJoinAndSelect('entry.student', 'student')
      .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
      .leftJoinAndSelect('entry.correction', 'correction')
      .where('entry.studentId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status IN (:...statuses)', {
        statuses: [
          NovelEntryStatus.SUBMITTED,
          NovelEntryStatus.UPDATED,
          NovelEntryStatus.CORRECTION_GIVEN,
          NovelEntryStatus.REVIEWED,
          NovelEntryStatus.UNDER_REVIEW
        ]
      })
      .orderBy('entry.submittedAt', 'DESC')
      .getMany();

    return entries.map(entry => this.mapEntryToResponseDto(entry));
  }

  async getEntryForReview(tutorId: string, entryId: string): Promise<NovelEntryResponseDto> {
    const entry = await this.novelEntryRepository.findOne({
      where: { id: entryId },
      relations: ['topic', 'student', 'feedbacks', 'correction']
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    // Verify tutor has access to this student
    const tutorMapping = await this.studentTutorMappingRepository.findOne({
      where: {
        tutorId,
        studentId: entry.studentId,
        status: MappingStatus.ACTIVE
      }
    });

    if (!tutorMapping) {
      throw new BadRequestException('You do not have access to this student\'s entry');
    }

    return this.mapEntryToResponseDto(entry);
  }

  async submitFeedback(tutorId: string, entryId: string, createFeedbackDto: CreateNovelFeedbackDto): Promise<NovelFeedbackResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const entry = await this.novelEntryRepository.findOne({
        where: { id: entryId },
        relations: ['student']
      });

      if (!entry) {
        throw new NotFoundException('Entry not found');
      }

      // Verify tutor has access
      const tutorMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          tutorId,
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE
        }
      });

      if (!tutorMapping) {
        throw new BadRequestException('You do not have access to this student\'s entry');
      }

      if (entry.status === NovelEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide feedback on entry that has not been submitted');
      }

      const feedback = this.novelFeedbackRepository.create({
        entryId,
        tutorId,
        feedback: createFeedbackDto.feedback
      });

      const savedFeedback = await queryRunner.manager.save(feedback);
      await queryRunner.commitTransaction();

      // Send notification to student
      try {
        await this.sendFeedbackNotification(entry, savedFeedback);
      } catch (notificationError) {
        this.logger.error(`Failed to send notification: ${notificationError.message}`, notificationError.stack);
      }

      this.logger.log(`Tutor ${tutorId} submitted feedback for entry ${entryId}`);
      return this.mapFeedbackToResponseDto(savedFeedback);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error submitting feedback: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async submitCorrection(tutorId: string, entryId: string, createCorrectionDto: CreateNovelCorrectionDto): Promise<NovelCorrectionResponseDto> {
    this.logger.log(`submitCorrection called with tutorId: ${tutorId}, entryId: ${entryId}`);
    this.logger.log(`createCorrectionDto: ${JSON.stringify(createCorrectionDto)}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const entry = await this.novelEntryRepository.findOne({
        where: { id: entryId },
        relations: ['student', 'correction']
      });

      this.logger.log(`Found entry: ${entry ? entry.id : 'null'}, student: ${entry?.studentId}, has correction: ${!!entry?.correction}`);

      if (!entry) {
        throw new NotFoundException('Entry not found');
      }

      // Verify tutor has access
      const tutorMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          tutorId,
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE
        }
      });

      if (!tutorMapping) {
        throw new BadRequestException('You do not have access to this student\'s entry');
      }

      if (entry.status === NovelEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide correction on entry that has not been submitted');
      }

      // Prevent creating correction if one already exists
      if (entry.correction) {
        throw new BadRequestException('Correction already exists for this entry. Use PUT to update the correction text.');
      }

      // Create new correction without score
      this.logger.log(`Creating correction for entryId: ${entryId}, tutorId: ${tutorId}`);

      // Validate parameters before creating entity
      if (!entryId || typeof entryId !== 'string' || entryId.trim() === '') {
        throw new BadRequestException(`Invalid entryId: ${entryId}`);
      }
      if (!tutorId || typeof tutorId !== 'string' || tutorId.trim() === '') {
        throw new BadRequestException(`Invalid tutorId: ${tutorId}`);
      }

      // Try using repository create method with explicit data
      const correctionData = {
        entryId: entryId.trim(),
        tutorId: tutorId.trim(),
        correction: createCorrectionDto.correction,
        score: null
      };

      this.logger.log(`Correction data to create: ${JSON.stringify(correctionData)}`);

      const correction = this.novelCorrectionRepository.create(correctionData);

      this.logger.log(`Before save - correction.entryId: ${correction.entryId}, correction.tutorId: ${correction.tutorId}`);
      this.logger.log(`Correction object: ${JSON.stringify({
        entryId: correction.entryId,
        tutorId: correction.tutorId,
        correction: correction.correction,
        score: correction.score
      })}`);

      const savedCorrection = await queryRunner.manager.save(correction);

      this.logger.log(`After save - savedCorrection.entryId: ${savedCorrection.entryId}, savedCorrection.id: ${savedCorrection.id}`);

      // Update entry status to correction_given
      entry.status = NovelEntryStatus.CORRECTION_GIVEN;
      await queryRunner.manager.save(entry);

      await queryRunner.commitTransaction();

      this.logger.log(`Tutor ${tutorId} submitted correction for entry ${entryId}`);
      return this.mapCorrectionToResponseDto(savedCorrection);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error submitting correction: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async updateCorrection(tutorId: string, entryId: string, updateCorrectionDto: UpdateNovelCorrectionDto): Promise<NovelCorrectionResponseDto> {
    const entry = await this.novelEntryRepository.findOne({
      where: { id: entryId },
      relations: ['correction']
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    if (!entry.correction) {
      throw new NotFoundException('No correction found for this entry');
    }

    // Verify tutor has access and owns the correction
    const tutorMapping = await this.studentTutorMappingRepository.findOne({
      where: {
        tutorId,
        studentId: entry.studentId,
        status: MappingStatus.ACTIVE
      }
    });

    if (!tutorMapping || entry.correction.tutorId !== tutorId) {
      throw new BadRequestException('You do not have access to update this correction');
    }

    // Prevent score modification if score already exists and entry is not under review
    if (entry.correction.score !== null && entry.status !== NovelEntryStatus.UNDER_REVIEW) {
      throw new BadRequestException('Cannot modify score once it has been set. Score can only be set during the review process.');
    }

    // Only update correction text
    entry.correction.correction = updateCorrectionDto.correction;
    const updatedCorrection = await this.novelCorrectionRepository.save(entry.correction);

    this.logger.log(`Tutor ${tutorId} updated correction for entry ${entryId}`);
    return this.mapCorrectionToResponseDto(updatedCorrection);
  }

  async submitReview(tutorId: string, entryId: string, createReviewDto: CreateNovelReviewDto): Promise<NovelCorrectionResponseDto> {
    // Validate input parameters
    if (!entryId) {
      throw new BadRequestException('Entry ID is required');
    }
    if (!tutorId) {
      throw new BadRequestException('Tutor ID is required');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const entry = await this.novelEntryRepository.findOne({
        where: { id: entryId },
        relations: ['student', 'correction']
      });

      if (!entry) {
        throw new NotFoundException('Entry not found');
      }

      // Verify tutor has access
      const tutorMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          tutorId,
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE
        }
      });

      if (!tutorMapping) {
        throw new BadRequestException('You do not have access to this student\'s entry');
      }

      if (entry.status === NovelEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide review on entry that has not been submitted');
      }

      let correction: NovelCorrection;

      if (entry.correction) {
        // Check if score already exists
        if (entry.correction.score !== null) {
          // If already reviewed, allow further correction by changing status to under_review
          if (entry.status === NovelEntryStatus.REVIEWED) {
            entry.correction.correction = createReviewDto.correction;
            entry.correction.score = createReviewDto.score;
            correction = await queryRunner.manager.save(entry.correction);

            // Update entry status to under_review for further corrections
            entry.status = NovelEntryStatus.UNDER_REVIEW;
            entry.reviewedAt = new Date();
            await queryRunner.manager.save(entry);
          } else {
            throw new BadRequestException('Entry has already been reviewed and scored. Score cannot be modified.');
          }
        } else {
          // Update existing correction with score
          entry.correction.correction = createReviewDto.correction;
          entry.correction.score = createReviewDto.score;
          correction = await queryRunner.manager.save(entry.correction);

          // Update entry status to reviewed
          entry.status = NovelEntryStatus.REVIEWED;
          entry.reviewedAt = new Date();
          await queryRunner.manager.save(entry);
        }
      } else {
        // Create new correction with score
        this.logger.log(`Creating review correction for entryId: ${entryId}, tutorId: ${tutorId}`);

        // Validate parameters before creating entity
        if (!entryId || typeof entryId !== 'string' || entryId.trim() === '') {
          throw new BadRequestException(`Invalid entryId: ${entryId}`);
        }
        if (!tutorId || typeof tutorId !== 'string' || tutorId.trim() === '') {
          throw new BadRequestException(`Invalid tutorId: ${tutorId}`);
        }

        const correctionData = {
          entryId: entryId.trim(),
          tutorId: tutorId.trim(),
          correction: createReviewDto.correction,
          score: createReviewDto.score
        };

        this.logger.log(`Review correction data to create: ${JSON.stringify(correctionData)}`);

        correction = this.novelCorrectionRepository.create(correctionData);

        this.logger.log(`Before save - review correction.entryId: ${correction.entryId}, correction.tutorId: ${correction.tutorId}`);

        correction = await queryRunner.manager.save(correction);

        this.logger.log(`After save - review savedCorrection.entryId: ${correction.entryId}, savedCorrection.id: ${correction.id}`);

        // Update entry status to reviewed
        entry.status = NovelEntryStatus.REVIEWED;
        entry.reviewedAt = new Date();
        await queryRunner.manager.save(entry);
      }

      await queryRunner.commitTransaction();

      // Send notification to student
      try {
        await this.sendCorrectionNotification(entry, correction);
      } catch (notificationError) {
        this.logger.error(`Failed to send notification: ${notificationError.message}`, notificationError.stack);
      }

      this.logger.log(`Tutor ${tutorId} submitted review for entry ${entryId}`);
      return this.mapCorrectionToResponseDto(correction);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error submitting review: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async confirmEntry(tutorId: string, entryId: string): Promise<NovelEntryResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const entry = await this.novelEntryRepository.findOne({
        where: { id: entryId },
        relations: ['student', 'correction', 'topic', 'feedbacks']
      });

      if (!entry) {
        throw new NotFoundException('Entry not found');
      }

      // Verify tutor has access
      const tutorMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          tutorId,
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE
        }
      });

      if (!tutorMapping) {
        throw new BadRequestException('You do not have access to this student\'s entry');
      }

      // Check if entry has been reviewed and has a score
      if (entry.status !== NovelEntryStatus.REVIEWED || !entry.correction || entry.correction.score === null) {
        throw new BadRequestException('Entry must be reviewed and have a score before it can be confirmed');
      }

      // Update entry status to confirmed
      entry.status = NovelEntryStatus.CONFIRMED;
      const updatedEntry = await queryRunner.manager.save(entry);

      await queryRunner.commitTransaction();

      // Send notification to student
      try {
        await this.sendConfirmationNotification(entry);
      } catch (notificationError) {
        this.logger.error(`Failed to send confirmation notification: ${notificationError.message}`, notificationError.stack);
      }

      this.logger.log(`Tutor ${tutorId} confirmed entry ${entryId}`);
      return this.mapEntryToResponseDto(updatedEntry);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error confirming entry: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async sendConfirmationNotification(entry: NovelEntry): Promise<void> {
    try {
      await this.notificationHelper.notify(
        entry.studentId,
        NotificationType.NOVEL_REVIEW,
        'Novel Entry Confirmed',
        `Your novel entry has been confirmed by your tutor`,
        {
          relatedEntityId: entry.id,
          relatedEntityType: 'novel_entry',
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false
        }
      );
    } catch (error) {
      this.logger.warn(`Failed to send confirmation notification: ${error.message}`);
    }
  }

  private async sendFeedbackNotification(entry: NovelEntry, feedback: NovelFeedback): Promise<void> {
    try {
      await this.notificationHelper.notify(
        entry.studentId,
        NotificationType.NOVEL_FEEDBACK,
        'New Novel Feedback',
        `You have received feedback on your novel entry`,
        {
          relatedEntityId: entry.id,
          relatedEntityType: 'novel_entry',
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false
        }
      );
    } catch (error) {
      this.logger.warn(`Failed to send feedback notification: ${error.message}`);
    }
  }

  private async sendCorrectionNotification(entry: NovelEntry, correction: NovelCorrection): Promise<void> {
    try {
      await this.notificationHelper.notify(
        entry.studentId,
        NotificationType.NOVEL_REVIEW,
        'Novel Entry Reviewed',
        `Your novel entry has been reviewed and scored`,
        {
          relatedEntityId: entry.id,
          relatedEntityType: 'novel_entry',
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false
        }
      );
    } catch (error) {
      this.logger.warn(`Failed to send correction notification: ${error.message}`);
    }
  }

  private mapEntryToResponseDto(entry: NovelEntry): NovelEntryResponseDto {
    return {
      id: entry.id,
      topicId: entry.topicId,
      studentId: entry.studentId,
      content: entry.content,
      wordCount: entry.wordCount,
      status: entry.status,
      skinId: entry.skinId,
      backgroundColor: entry.backgroundColor,
      submittedAt: entry.submittedAt,
      reviewedAt: entry.reviewedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      topic: entry.topic ? {
        id: entry.topic.id,
        title: entry.topic.title,
        sequenceTitle: entry.topic.sequenceTitle,
        category: entry.topic.category,
        instruction: entry.topic.instruction,
        isActive: entry.topic.isActive,
        createdAt: entry.topic.createdAt,
        updatedAt: entry.topic.updatedAt
      } : undefined,
      feedbacks: entry.feedbacks?.map(feedback => this.mapFeedbackToResponseDto(feedback)),
      correction: entry.correction ? this.mapCorrectionToResponseDto(entry.correction) : undefined
    };
  }

  private mapFeedbackToResponseDto(feedback: NovelFeedback): NovelFeedbackResponseDto {
    return {
      id: feedback.id,
      entryId: feedback.entryId,
      tutorId: feedback.tutorId,
      feedback: feedback.feedback,
      createdAt: feedback.createdAt,
      updatedAt: feedback.updatedAt
    };
  }

  private mapCorrectionToResponseDto(correction: NovelCorrection): NovelCorrectionResponseDto {
    return {
      id: correction.id,
      entryId: correction.entryId,
      tutorId: correction.tutorId,
      correction: correction.correction,
      score: correction.score || undefined,
      createdAt: correction.createdAt,
      updatedAt: correction.updatedAt
    };
  }
}

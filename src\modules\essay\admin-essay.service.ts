import { Injectable, NotFoundException, BadRequestException, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, DataSource, ILike, In, Not, IsNull } from "typeorm";
import { EssayMissionTasks } from "src/database/entities/essay-mission-tasks.entity";
import { EssayTaskSubmissions,SubmissionStatus } from "src/database/entities/essay-task-submissions.entity";
import { EssayTaskSubmissionHistory } from "src/database/entities/essay-task-submission-history.entity";
import { EssayMission, MissionFrequency } from "src/database/entities/essay-mission.entity";
import {
  MissionResponseDto,
  MissionPaginationDto,
  CreateMissionDto,
  UpdateMissionDto,
  EssayTaskSubmissionDto
} from "src/database/models/mission.dto";
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { PaginationDto } from "src/common/models/pagination.dto";
import { UsersService } from "../users/users.service";
import { EssayModuleSkinPreference, SkinScopeType } from "src/database/entities/essay-preferences.entity";


@Injectable()
export class EssayMissionService {
  private readonly logger = new Logger(EssayMissionService.name);

  constructor(
    @InjectRepository(EssayMission)
    private readonly missionRepository: Repository<EssayMission>,
    @InjectRepository(EssayMissionTasks)
    private readonly missionTasksRepository: Repository<EssayMissionTasks>,
    @InjectRepository(EssayTaskSubmissions)
    private readonly essayTaskSubmissionsRepository: Repository<EssayTaskSubmissions>,
    @InjectRepository(EssayTaskSubmissionHistory)
    private readonly essayTaskSubmissionHistoryRepository: Repository<EssayTaskSubmissionHistory>,
    @InjectRepository(EssayModuleSkinPreference)
    private readonly essayModuleSkinPreferenceRepository: Repository<EssayModuleSkinPreference>,
    private readonly dataSource: DataSource,
    private readonly userService: UsersService,
  ) {}

  
private toMissionResponseDto(mission: EssayMission, tasks: EssayMissionTasks[], userId?: string): MissionResponseDto {
  const taskProgress = tasks.flatMap(task => {

    if (!userId) {
      return [{
        id: task.id,
        progress: 0,
        status: null
      }];
    }
    const firstRevisionSubmission = task.submissions?.find(submission => 
      (submission.isFirstRevision || submission.currentRevision === 1) && submission.createdBy === userId
    );
    
    if (firstRevisionSubmission) {
      return [{
        id: task.id,
        progress: firstRevisionSubmission.firstRevisionProgress || 0,
        status: firstRevisionSubmission.status,
      }];
    }
    return [{
      id: task.id,
      progress: 0,
      status: null
    }];
  });
  
  return {
    id: mission.id,
    timeFrequency: mission.timeFrequency,
    isActive: mission.isActive,
    sequenceNumber: mission.sequenceNumber,
    tasks: tasks.map(task => ({
      id: task.id,
      title: task.title,
      description: task.description,
      wordLimitMinimum: task.wordLimitMinimum,
      wordLimitMaximum: task.wordLimitMaximum,
      isActive: task.isActive,
      timePeriodUnit: task.timePeriodUnit,
      deadline: task.deadline,
      instructions: task.instructions,
      metaData: task.metaData,
    })),
    taskProgress
  };
}

  async findAll(paginationDto?: MissionPaginationDto, userId?: string): Promise<PagedListDto<MissionResponseDto>> {
    let options: any = {
      relations: ['tasks', 'tasks.submissions'],
      where: { isActive: true },
      order: {}
    };

    if (paginationDto) {
      const { page = 1, limit = 10, sortBy, sortDirection, timeFrequency, weekOrMonth, title } = paginationDto;
      if (timeFrequency) {
        options.where.timeFrequency = timeFrequency;
      }
      options.skip = (page - 1) * limit;
      options.take = limit;

      if (sortBy) {
        if (sortBy == 'week' || sortBy == 'month') {
          options.where.sequenceNumber = weekOrMonth;
        } else if (sortBy == 'title') {
          options.where.tasks = { title: ILike(`%${title}%`) };
        }
        if (sortBy == 'createdAt' || sortBy == 'updatedAt') {
          if (sortDirection) {
            options.order = { [sortBy]: sortDirection.toUpperCase() };
          } else {
            options.order = { [sortBy]: 'DESC' };
          }
        }
      }
    }

    options.order.sequenceNumber = 'ASC'; // Default sort by sequence number
    const [missions, totalCount] = await this.missionRepository.findAndCount(options);
    

    if(userId) {
      const missionDtos = missions.map(mission =>
        this.toMissionResponseDto(mission, mission.tasks, userId)
      );
      return new PagedListDto(missionDtos, totalCount);
    }
    
    const missionDtos = missions.map(mission =>
      this.toMissionResponseDto(mission, mission.tasks)
    );

    return new PagedListDto(missionDtos, totalCount);
  }

  async findById(id: string): Promise<MissionResponseDto> {
    const mission = await this.missionRepository.findOne({
      where: { id },
      relations: ['tasks']
    });

    if (!mission) {
      throw new NotFoundException(`Mission with ID ${id} not found`);
    }

    return this.toMissionResponseDto(mission, mission.tasks);
  }

  async create(missionData: CreateMissionDto): Promise<MissionResponseDto> {
    try {
      const { timeFrequency, tasks } = missionData;

      const lastMission = await this.missionRepository.findOne({
        order: { sequenceNumber: 'DESC' },
        where: { isActive: true, timeFrequency: timeFrequency }
      });

      const lastSequenceNumber = lastMission ? lastMission.sequenceNumber + 1: 1;

      if (timeFrequency == MissionFrequency.WEEKLY && lastSequenceNumber > 52 ) {
        throw new BadRequestException('Cannot create more than 52 missions ( yearly ) for weekly frequencies');
      }

      if (timeFrequency == MissionFrequency.MONTHLY && lastSequenceNumber > 12) {
        throw new BadRequestException('Cannot create more than 12 missions for monthly ( yearly ) frequencies');
      }

      const mission = this.missionRepository.create({
        timeFrequency,
        isActive: true,
        sequenceNumber: lastSequenceNumber,
      });

      const savedMission = await this.missionRepository.save(mission);

      const missionTasks = tasks.map(task =>
        this.missionTasksRepository.create({
          ...task,
          mission: savedMission,
          missionId: savedMission.id,
          isActive: true,
        })
      );

      const savedTasks = await this.missionTasksRepository.save(missionTasks);

      return this.toMissionResponseDto(savedMission, savedTasks);
    } catch (error) {
      this.logger.error(`Failed to create mission: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to create mission. Please check your input data.');
    }
  }

  async update(id: string, missionData: UpdateMissionDto): Promise<MissionResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const mission = await queryRunner.manager.findOne(EssayMission, {
        where: { id },
        relations: ['tasks']
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      const { tasks: incomingTasksData, ...missionProperties } = missionData;
      Object.assign(mission, missionProperties);
      await queryRunner.manager.save(EssayMission, mission);

      if (incomingTasksData && incomingTasksData.length > 0) {
        const incomingTaskIds = incomingTasksData
          .filter(task => task.id)
          .map(task => task.id);

        const tasksToDelete = mission.tasks?.filter(
          task => !incomingTaskIds.includes(task.id)
        ) || [];

        if (tasksToDelete.length > 0) {
          await queryRunner.manager.delete(EssayMissionTasks,
            tasksToDelete.map(task => task.id)
          );
        }

        for (const taskData of incomingTasksData) {
          if (taskData.id) {
            await queryRunner.manager.update(
              EssayMissionTasks,
              { id: taskData.id },
              {
                ...taskData,
                mission: { id: mission.id },
                missionId: mission.id,
              }
            );
          } else {
            const newTask = queryRunner.manager.create(EssayMissionTasks, {
              ...taskData,
              mission: { id: mission.id },
              missionId: mission.id,
            });
            await queryRunner.manager.save(EssayMissionTasks, newTask);
          }
        }
      }

      const updatedMission = await queryRunner.manager.findOne(EssayMission, {
        where: { id: mission.id },
        relations: ['tasks']
      });

      await queryRunner.commitTransaction();

      return this.toMissionResponseDto(
        updatedMission!,
        updatedMission?.tasks || []
      );

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update mission ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to update mission: ${error.message || 'Please check input data'}`);
    } finally {
      await queryRunner.release();
    }
  }

  async softDelete(id: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const mission = await queryRunner.manager.findOne(EssayMission, {
        where: { id },
        relations: ['tasks']
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      mission.isActive = false;
      await queryRunner.manager.save(EssayMission, mission);

      if (mission.tasks && mission.tasks.length > 0) {
        await queryRunner.manager.update(EssayMissionTasks,
          { missionId: mission.id },
          { isActive: false }
        );
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to soft delete mission ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to soft delete mission: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async hardDelete(id: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const mission = await queryRunner.manager.findOne(EssayMission, {
        where: { id },
        relations: ['tasks']
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      if (mission.tasks && mission.tasks.length > 0) {
        await queryRunner.manager.delete(EssayMissionTasks,
          mission.tasks.map(task => task.id)
        );
      }

      await queryRunner.manager.delete(EssayMission, id);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to delete mission ${id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to delete mission: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async findByFrequency(frequency: MissionFrequency): Promise<MissionResponseDto[]> {
    const missions = await this.missionRepository.find({
      where: { timeFrequency: frequency, isActive: true },
      relations: ['tasks']
    });

    return missions.map(mission => this.toMissionResponseDto(mission, mission.tasks));
  }

  async findAllSubmittedEssays(paginationDto?: PaginationDto): Promise<PagedListDto<EssayTaskSubmissionDto>> {
    const { page = 1, limit = 10} = paginationDto;

    const minSubmissionDates = await this.essayTaskSubmissionsRepository
      .createQueryBuilder("submission")
      .select("submission.taskId", "taskId")
      .addSelect("MIN(submission.firstSubmittedAt)", "minSubmittedAt")
      .where("submission.status IN (:...statuses)", { 
        statuses: [SubmissionStatus.SUBMITTED, SubmissionStatus.REVIEWED] 
      })
      .andWhere("submission.firstSubmittedAt IS NOT NULL")
      .groupBy("submission.taskId")
      .getRawMany();
    
    const firstSubmissions : EssayTaskSubmissions[] = [];
    const skip = (page - 1) * limit;
    const paginatedMinDates = minSubmissionDates.slice(skip, skip + limit);
    
    for (const { taskId, minSubmittedAt } of paginatedMinDates) {
      const submission = await this.essayTaskSubmissionsRepository.findOne({
        where: {
          taskId: taskId,
          firstSubmittedAt: minSubmittedAt,
          status: In([SubmissionStatus.SUBMITTED, SubmissionStatus.REVIEWED])
        },
        relations: ['submissionSkin','task']
      });

      if (submission) {
        const submissionHistory = await this.essayTaskSubmissionHistoryRepository.find({
          where: {
            id: submission.latestSubmissionId
          },
          relations: ['submissionMark']
        });
        submission.submissionHistory = submissionHistory;
        firstSubmissions.push(submission);
      }
    }
    const totalCount = firstSubmissions.length;
    const firstSubmissionsDto = await Promise.all(
      firstSubmissions.map(submission => this.toEssayTaskSubmissionDto(submission))
    );    
    return new PagedListDto(firstSubmissionsDto, totalCount);
  }

  async findSubmittedEssayById(id: string): Promise<EssayTaskSubmissionDto> {
    const submission = await this.essayTaskSubmissionsRepository.findOne({
      where: { id },
      relations: ['submissionSkin', 'task']
    });

    if (!submission) {
      throw new NotFoundException(`Submitted essay with ID ${id} not found`);
    }

    const submissionHistory = await this.essayTaskSubmissionHistoryRepository.find({
      where: {
        id: submission.latestSubmissionId
      },
      relations: ['submissionMark']
    });
    submission.submissionHistory = submissionHistory;

    const essayTaskSubmissionDto = await this.toEssayTaskSubmissionDto(submission);
    return essayTaskSubmissionDto;
  }

  private async toEssayTaskSubmissionDto(submission: EssayTaskSubmissions): Promise<EssayTaskSubmissionDto> {
    const submissionUpdatedBy = await this.userService.findById(submission.updatedBy);
    const submissionCreatedBy = await this.userService.findById(submission.createdBy);
    
    const submissionHistoryPromises = submission.submissionHistory.map(async (history) => {
      const historyUpdatedBy = await this.userService.findById(history.updatedBy);
      const historyCreatedBy = await this.userService.findById(history.createdBy);
      
      let submissionMark = null;
      if (history.submissionMark) {
        const markUpdatedBy = await this.userService.findById(history.submissionMark.updatedBy);
        
        submissionMark = {
          id: history.submissionMark.id,
          points: history.submissionMark.points,
          submissionFeedback: history.submissionMark.submissionFeedback,
          taskRemarks: history.submissionMark.taskRemarks,
          createdAt: history.submissionMark.createdAt,
          updatedAt: history.submissionMark.updatedAt,
          updatedBy: markUpdatedBy?.name || null,
          updatedById: markUpdatedBy?.id || null,
        };
      }
      
      return {
        id: history.id,
        content: history.content,
        metaData: history.metaData,
        createdAt: history.createdAt,
        updatedAt: history.updatedAt,
        submissionMark,
        wordCount: history.wordCount,
        submissionDate: history.submissionDate,
        sequenceNumber: history.sequenceNumber,
        updatedBy: historyUpdatedBy?.name || null,
        updatedById: historyUpdatedBy?.id || null,
        createdBy: historyCreatedBy?.name || null,
        createdById: historyCreatedBy?.id || null,
      };
    });
    
    const processedHistory = await Promise.all(submissionHistoryPromises);

    const diarySkin = submission.submissionSkin ?? 
      (await this.essayModuleSkinPreferenceRepository.findOne({
        where: {
          scopeType: SkinScopeType.MODULE_DEFAULT,
          isActive: true
        }
      }))?.skin;
    
    const response: EssayTaskSubmissionDto = {
      id: submission.id,
      title: submission.title,
      status: submission.status,
      currentRevision: submission.currentRevision,
      task: {
        id: submission.task.id,
        wordLimitMinimum: submission.task.wordLimitMinimum,
        wordLimitMaximum: submission.task.wordLimitMaximum,
      },
      updatedBy: submissionUpdatedBy?.name || null,
      updatedById: submissionUpdatedBy?.id || null,
      createdAt: submission.createdAt,
      updatedAt: submission.updatedAt,
      submissionHistory: processedHistory,
      isActive: submission.isActive,
      diarySkin: diarySkin,
      createdBy: submissionCreatedBy?.name || null,
      createdById: submissionCreatedBy?.id || null,
    };
    
    return response;
  }
}
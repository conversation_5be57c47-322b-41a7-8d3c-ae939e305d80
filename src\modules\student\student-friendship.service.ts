import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { StudentFriendship, FriendshipStatus } from '../../database/entities/student-friendship.entity';
import { DiaryFollowRequest, FollowRequestStatus } from '../../database/entities/diary-follow-request.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { Conversation } from '../../database/entities/conversation.entity';
import {
  SendFriendRequestDto,
  SendDiaryFollowRequestDto,
  RespondToRequestDto,
  FriendshipResponseDto,
  StudentSearchResultDto,
  PendingRequestsResponseDto,
  DiaryFollowRequestResponseDto,
  ConversationByFriendResponseDto
} from '../../database/models/student-friendship.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { ChatService } from '../chat/chat.service';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';

@Injectable()
export class StudentFriendshipService {
  private readonly logger = new Logger(StudentFriendshipService.name);

  constructor(
    @InjectRepository(StudentFriendship)
    private studentFriendshipRepository: Repository<StudentFriendship>,
    @InjectRepository(DiaryFollowRequest)
    private diaryFollowRequestRepository: Repository<DiaryFollowRequest>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Conversation)
    private conversationRepository: Repository<Conversation>,
    private readonly fileRegistryService: FileRegistryService,
    private readonly profilePictureService: ProfilePictureService,
    private readonly chatService: ChatService,
    private readonly notificationService: NotificationHelperService
  ) {}

  /**
   * Search for students by name, ID, email, or phone
   * @param query Search query
   * @param type Search type (id, name, email, phone)
   * @param currentUserId ID of the current user
   * @param paginationDto Pagination parameters
   * @returns Paged list of student search results
   */
  async searchStudents(
    query: string,
    type: string,
    currentUserId: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<StudentSearchResultDto>> {
    try {
      this.logger.log(`Searching for students with query: ${query}, type: ${type}`);

      // Default pagination
      const page = paginationDto?.page || 1;
      const limit = paginationDto?.limit || 10;
      const skip = (page - 1) * limit;

      // Check if query has at least 3 characters for partial search
      const isPartialSearch = query.length >= 3;
      this.logger.log(`Using ${isPartialSearch ? 'partial' : 'exact'} search for query: ${query}`);

      // Use raw SQL query instead of query builder to avoid syntax issues
      let sqlQuery = `
        SELECT u.id, u.user_id as "userId", u.name, u.profile_picture as "profilePicture"
        FROM "user" u
        WHERE u.type = $1 AND u.id != $2
      `;

      const params: any[] = [UserType.STUDENT, currentUserId];
      let paramIndex = 3;

      // Add search criteria based on type
      if (type === 'id') {
        if (isPartialSearch) {
          sqlQuery += ` AND u.user_id ILIKE $${paramIndex}`;
          params.push(`%${query}%`);
        } else {
          sqlQuery += ` AND u.user_id = $${paramIndex}`;
          params.push(query);
        }
        paramIndex++;
      } else if (type === 'name') {
        if (isPartialSearch) {
          sqlQuery += ` AND u.name ILIKE $${paramIndex}`;
          params.push(`%${query}%`);
        } else {
          sqlQuery += ` AND u.name = $${paramIndex}`;
          params.push(query);
        }
        paramIndex++;
      } else if (type === 'email') {
        if (isPartialSearch) {
          sqlQuery += ` AND u.email ILIKE $${paramIndex}`;
          params.push(`%${query}%`);
        } else {
          sqlQuery += ` AND u.email = $${paramIndex}`;
          params.push(query);
        }
        paramIndex++;
      } else if (type === 'phone') {
        if (isPartialSearch) {
          sqlQuery += ` AND u.phone_number ILIKE $${paramIndex}`;
          params.push(`%${query}%`);
        } else {
          sqlQuery += ` AND u.phone_number = $${paramIndex}`;
          params.push(query);
        }
        paramIndex++;
      } else {
        // If no specific type, search across all fields
        if (isPartialSearch) {
          const searchPattern = `%${query}%`;
          sqlQuery += ` AND (u.user_id ILIKE $${paramIndex} OR u.name ILIKE $${paramIndex + 1} OR u.email ILIKE $${paramIndex + 2} OR u.phone_number ILIKE $${paramIndex + 3})`;
          params.push(searchPattern, searchPattern, searchPattern, searchPattern);
          paramIndex += 4; // Increment by 4 since we added 4 parameters
        } else {
          sqlQuery += ` AND (u.user_id = $${paramIndex} OR u.name = $${paramIndex + 1} OR u.email = $${paramIndex + 2} OR u.phone_number = $${paramIndex + 3})`;
          params.push(query, query, query, query);
          paramIndex += 4; // Increment by 4 since we added 4 parameters
        }
      }

      // Add order by, limit and offset
      sqlQuery += ` ORDER BY u.name ASC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      params.push(limit);
      params.push(skip);

      // Log the query and parameters for debugging
      this.logger.log(`SQL Query: ${sqlQuery}`);
      this.logger.log(`Parameters: ${JSON.stringify(params)}`);

      // Execute the query
      const students = await this.userRepository.query(sqlQuery, params);

      // Get the count with a separate query
      let countQuery = `
        SELECT COUNT(*) as total
        FROM "user" u
        WHERE u.type = $1 AND u.id != $2
      `;

      // Reset paramIndex for count query
      paramIndex = 3;

      // Add the same search criteria to count query
      if (type === 'id') {
        if (isPartialSearch) {
          countQuery += ` AND u.user_id ILIKE $${paramIndex}`;
        } else {
          countQuery += ` AND u.user_id = $${paramIndex}`;
        }
        paramIndex++;
      } else if (type === 'name') {
        if (isPartialSearch) {
          countQuery += ` AND u.name ILIKE $${paramIndex}`;
        } else {
          countQuery += ` AND u.name = $${paramIndex}`;
        }
        paramIndex++;
      } else if (type === 'email') {
        if (isPartialSearch) {
          countQuery += ` AND u.email ILIKE $${paramIndex}`;
        } else {
          countQuery += ` AND u.email = $${paramIndex}`;
        }
        paramIndex++;
      } else if (type === 'phone') {
        if (isPartialSearch) {
          countQuery += ` AND u.phone_number ILIKE $${paramIndex}`;
        } else {
          countQuery += ` AND u.phone_number = $${paramIndex}`;
        }
        paramIndex++;
      } else {
        // If no specific type, search across all fields
        if (isPartialSearch) {
          countQuery += ` AND (u.user_id ILIKE $${paramIndex} OR u.name ILIKE $${paramIndex + 1} OR u.email ILIKE $${paramIndex + 2} OR u.phone_number ILIKE $${paramIndex + 3})`;
          // Note: We don't need to push parameters here as we're using params.slice(0, paramIndex) below
          // The parameters are already in the params array from the main query
        } else {
          countQuery += ` AND (u.user_id = $${paramIndex} OR u.name = $${paramIndex + 1} OR u.email = $${paramIndex + 2} OR u.phone_number = $${paramIndex + 3})`;
          // Note: We don't need to push parameters here as we're using params.slice(0, paramIndex) below
          // The parameters are already in the params array from the main query
        }
        paramIndex += 4; // Increment by 4 to match the main query
      }

      // Log the count query and parameters for debugging
      this.logger.log(`Count Query: ${countQuery}`);

      // Create a new array with the correct parameters for the count query
      // We need to exclude the LIMIT and OFFSET parameters which are at the end of the params array
      const countParams = params.slice(0, params.length - 2);

      this.logger.log(`Count Parameters: ${JSON.stringify(countParams)}`);

      // Execute the count query
      const countResult = await this.userRepository.query(countQuery, countParams);
      const total = parseInt(countResult[0].total, 10);

      // Get all friendship statuses for the current user
      const friendships = await this.studentFriendshipRepository.find({
        where: [
          { requesterId: currentUserId },
          { requestedId: currentUserId }
        ]
      });

      // Map students to response DTOs
      const results = await Promise.all(students.map(async (student) => {
        // Check friendship status
        const friendship = friendships.find(f =>
          (f.requesterId === currentUserId && f.requestedId === student.id) ||
          (f.requesterId === student.id && f.requestedId === currentUserId)
        );

        let friendshipStatus = 'none';
        let isFriend = false;
        let canViewDiary = false;

        if (friendship) {
          friendshipStatus = friendship.status;
          isFriend = friendship.status === FriendshipStatus.ACCEPTED;
          canViewDiary = isFriend && friendship.canViewDiary;
        }

        // Get profile picture URL if available
        let profilePicture = null;
        if (student.profilePicture) {
          profilePicture = await this.fileRegistryService.getFileUrlWithFallback(
            FileEntityType.PROFILE_PICTURE,
            student.id
          );
        }

        return {
          id: student.id,
          userId: student.userId,
          name: student.name,
          profilePicture,
          friendshipStatus,
          isFriend,
          canViewDiary
        };
      }));

      return new PagedListDto(
        results,
        total,
        page,
        limit
      );
    } catch (error) {
      this.logger.error(`Error searching for students: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send a friend request to another student
   * @param requesterId ID of the student sending the request
   * @param sendFriendRequestDto Friend request data
   * @returns The created friendship
   */
  async sendFriendRequest(
    requesterId: string,
    sendFriendRequestDto: SendFriendRequestDto
  ): Promise<FriendshipResponseDto> {
    try {
      const { requestedId, requestMessage } = sendFriendRequestDto;

      // Check if the requester exists and is a student
      const requester = await this.userRepository.findOne({
        where: { id: requesterId, type: UserType.STUDENT }
      });

      if (!requester) {
        throw new NotFoundException(`Student with ID ${requesterId} not found`);
      }

      // Check if the requested user exists and is a student
      const requested = await this.userRepository.findOne({
        where: { id: requestedId, type: UserType.STUDENT }
      });

      if (!requested) {
        throw new NotFoundException(`Student with ID ${requestedId} not found`);
      }

      // Check if a friendship already exists
      const existingFriendship = await this.studentFriendshipRepository.findOne({
        where: [
          { requesterId, requestedId },
          { requesterId: requestedId, requestedId: requesterId }
        ]
      });

      if (existingFriendship) {
        if (existingFriendship.status === FriendshipStatus.ACCEPTED) {
          throw new ConflictException('You are already friends with this student');
        } else if (existingFriendship.status === FriendshipStatus.PENDING) {
          if (existingFriendship.requesterId === requesterId) {
            throw new ConflictException('You have already sent a friend request to this student');
          } else {
            throw new ConflictException('This student has already sent you a friend request');
          }
        } else if (existingFriendship.status === FriendshipStatus.REJECTED) {
          // If the request was previously rejected, update it to pending
          existingFriendship.status = FriendshipStatus.PENDING;
          existingFriendship.requestMessage = requestMessage;

          const updatedFriendship = await this.studentFriendshipRepository.save(existingFriendship);

          return this.mapFriendshipToResponseDto(updatedFriendship, requester, requested);
        }
      }

      // Create a new friendship
      const friendship = this.studentFriendshipRepository.create({
        requesterId,
        requestedId,
        status: FriendshipStatus.PENDING,
        requestMessage,
        canViewDiary: false
      });

      const savedFriendship = await this.studentFriendshipRepository.save(friendship);

      // Send notification to the requested user
      await this.notificationService.notify(
        requestedId,
        NotificationType.SYSTEM,
        'New Friend Request',
        `${requester.name} has sent you a friend request.`,
        {
          relatedEntityId: savedFriendship.id,
          relatedEntityType: 'friendship',
          htmlContent: `<p><strong>${requester.name}</strong> has sent you a friend request.</p>
                       <p>Message: "${requestMessage}"</p>`,
          sendInApp: true,
          sendPush: true
        }
      );

      return this.mapFriendshipToResponseDto(savedFriendship, requester, requested);
    } catch (error) {
      this.logger.error(`Error sending friend request: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send a diary follow request to another student
   * @param requesterId ID of the student sending the request
   * @param sendDiaryFollowRequestDto Diary follow request data
   * @returns The created diary follow request
   */
  async sendDiaryFollowRequest(
    requesterId: string,
    sendDiaryFollowRequestDto: SendDiaryFollowRequestDto
  ): Promise<DiaryFollowRequestResponseDto> {
    try {
      const { diaryOwnerId, requestMessage } = sendDiaryFollowRequestDto;

      // Check if the requester exists and is a student
      const requester = await this.userRepository.findOne({
        where: { id: requesterId, type: UserType.STUDENT }
      });

      if (!requester) {
        throw new NotFoundException(`Student with ID ${requesterId} not found`);
      }

      // Check if the diary owner exists and is a student
      const diaryOwner = await this.userRepository.findOne({
        where: { id: diaryOwnerId, type: UserType.STUDENT }
      });

      if (!diaryOwner) {
        throw new NotFoundException(`Student with ID ${diaryOwnerId} not found`);
      }

      // Check if a friendship exists
      let friendship = await this.studentFriendshipRepository.findOne({
        where: [
          { requesterId, requestedId: diaryOwnerId, status: FriendshipStatus.ACCEPTED },
          { requesterId: diaryOwnerId, requestedId: requesterId, status: FriendshipStatus.ACCEPTED }
        ]
      });

      // If no friendship exists, create a friend request first
      if (!friendship) {
        // Create a friendship request
        const newFriendship = this.studentFriendshipRepository.create({
          requesterId,
          requestedId: diaryOwnerId,
          status: FriendshipStatus.PENDING,
          requestMessage: requestMessage || 'I would like to follow your diary',
          canViewDiary: false
        });

        friendship = await this.studentFriendshipRepository.save(newFriendship);
      }

      // Check if a diary follow request already exists
      const existingRequest = await this.diaryFollowRequestRepository.findOne({
        where: { requesterId, diaryOwnerId }
      });

      if (existingRequest) {
        if (existingRequest.status === FollowRequestStatus.ACCEPTED) {
          throw new ConflictException('You are already following this student\'s diary');
        } else if (existingRequest.status === FollowRequestStatus.PENDING) {
          throw new ConflictException('You have already sent a diary follow request to this student');
        } else if (existingRequest.status === FollowRequestStatus.REJECTED) {
          // If the request was previously rejected, update it to pending
          existingRequest.status = FollowRequestStatus.PENDING;
          existingRequest.requestMessage = requestMessage;
          existingRequest.friendshipId = friendship.id;

          const updatedRequest = await this.diaryFollowRequestRepository.save(existingRequest);

          return this.mapDiaryFollowRequestToResponseDto(updatedRequest, requester, diaryOwner);
        }
      }

      // Create a new diary follow request
      const followRequest = this.diaryFollowRequestRepository.create({
        requesterId,
        diaryOwnerId,
        friendshipId: friendship.id,
        status: FollowRequestStatus.PENDING,
        requestMessage
      });

      const savedRequest = await this.diaryFollowRequestRepository.save(followRequest);

      // Send notification to the diary owner
      await this.notificationService.notify(
        diaryOwnerId,
        NotificationType.SYSTEM,
        'New Diary Follow Request',
        `${requester.name} has requested to follow your diary.`,
        {
          relatedEntityId: savedRequest.id,
          relatedEntityType: 'diary_follow_request',
          htmlContent: `<p><strong>${requester.name}</strong> has requested to follow your diary.</p>
                       <p>Message: "${requestMessage || 'I would like to follow your diary'}"</p>`,
          sendInApp: true,
          sendPush: true
        }
      );

      return this.mapDiaryFollowRequestToResponseDto(savedRequest, requester, diaryOwner);
    } catch (error) {
      this.logger.error(`Error sending diary follow request: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get pending friend requests for a student
   * @param userId ID of the student
   * @returns Pending friend requests
   */
  async getPendingFriendRequests(userId: string): Promise<PendingRequestsResponseDto> {
    try {
      // Check if the user exists and is a student
      const user = await this.userRepository.findOne({
        where: { id: userId, type: UserType.STUDENT }
      });

      if (!user) {
        throw new NotFoundException(`Student with ID ${userId} not found`);
      }

      // Get incoming requests (where the user is the requested)
      const incomingRequests = await this.studentFriendshipRepository.find({
        where: { requestedId: userId, status: FriendshipStatus.PENDING },
        order: { createdAt: 'DESC' }
      });

      // Get outgoing requests (where the user is the requester)
      const outgoingRequests = await this.studentFriendshipRepository.find({
        where: { requesterId: userId, status: FriendshipStatus.PENDING },
        order: { createdAt: 'DESC' }
      });

      // Get all user IDs involved in the requests
      const userIds = new Set<string>();
      incomingRequests.forEach(request => userIds.add(request.requesterId));
      outgoingRequests.forEach(request => userIds.add(request.requestedId));

      // Get all users involved in the requests
      const users = await this.userRepository.find({
        where: { id: In([...userIds]) },
        select: ['id', 'name', 'profilePicture']
      });

      // Map users to a dictionary for easy lookup
      const userMap = new Map<string, User>();
      users.forEach(user => userMap.set(user.id, user));

      // Map incoming requests to response DTOs
      const incomingRequestDtos = await Promise.all(incomingRequests.map(async (request) => {
        const requester = userMap.get(request.requesterId);
        const requested = userMap.get(request.requestedId);
        return this.mapFriendshipToResponseDto(request, requester, requested);
      }));

      // Map outgoing requests to response DTOs
      const outgoingRequestDtos = await Promise.all(outgoingRequests.map(async (request) => {
        const requester = userMap.get(request.requesterId);
        const requested = userMap.get(request.requestedId);
        return this.mapFriendshipToResponseDto(request, requester, requested);
      }));

      return {
        incomingRequests: incomingRequestDtos,
        outgoingRequests: outgoingRequestDtos
      };
    } catch (error) {
      this.logger.error(`Error getting pending friend requests: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get pending diary follow requests for a student
   * @param userId ID of the student
   * @returns Pending diary follow requests
   */
  async getPendingDiaryFollowRequests(userId: string): Promise<DiaryFollowRequestResponseDto[]> {
    try {
      // Check if the user exists and is a student
      const user = await this.userRepository.findOne({
        where: { id: userId, type: UserType.STUDENT }
      });

      if (!user) {
        throw new NotFoundException(`Student with ID ${userId} not found`);
      }

      // Get incoming requests (where the user is the diary owner)
      const incomingRequests = await this.diaryFollowRequestRepository.find({
        where: { diaryOwnerId: userId, status: FollowRequestStatus.PENDING },
        order: { createdAt: 'DESC' }
      });

      // Get all user IDs involved in the requests
      const userIds = new Set<string>();
      incomingRequests.forEach(request => userIds.add(request.requesterId));

      // Get all users involved in the requests
      const users = await this.userRepository.find({
        where: { id: In([...userIds]) },
        select: ['id', 'name', 'profilePicture']
      });

      // Map users to a dictionary for easy lookup
      const userMap = new Map<string, User>();
      users.forEach(user => userMap.set(user.id, user));

      // Map incoming requests to response DTOs
      return await Promise.all(incomingRequests.map(async (request) => {
        const requester = userMap.get(request.requesterId);
        const diaryOwner = user;
        return this.mapDiaryFollowRequestToResponseDto(request, requester, diaryOwner);
      }));
    } catch (error) {
      this.logger.error(`Error getting pending diary follow requests: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Accept or reject a friend request
   * @param requestId ID of the friend request
   * @param userId ID of the user responding to the request
   * @param respondToRequestDto Response data
   * @returns The updated friendship
   */
  async respondToFriendRequest(
    requestId: string,
    userId: string,
    respondToRequestDto: RespondToRequestDto
  ): Promise<FriendshipResponseDto> {
    try {
      // Find the friendship
      const friendship = await this.studentFriendshipRepository.findOne({
        where: { id: requestId, requestedId: userId, status: FriendshipStatus.PENDING }
      });

      if (!friendship) {
        throw new NotFoundException(`Friend request with ID ${requestId} not found`);
      }

      // Update the friendship status
      friendship.status = respondToRequestDto.status;

      // Save the updated friendship
      const updatedFriendship = await this.studentFriendshipRepository.save(friendship);

      // Get the users involved
      const requester = await this.userRepository.findOne({
        where: { id: friendship.requesterId },
        select: ['id', 'name', 'profilePicture']
      });

      const requested = await this.userRepository.findOne({
        where: { id: friendship.requestedId },
        select: ['id', 'name', 'profilePicture']
      });

      // If accepted, create a chat conversation between the users
      if (friendship.status === FriendshipStatus.ACCEPTED) {
        try {
          // Check if a conversation already exists
          const conversation = await this.chatService.getOrCreateConversation(
            friendship.requesterId,
            friendship.requestedId
          );

          this.logger.log(`Created or retrieved chat conversation ${conversation.id} between users ${friendship.requesterId} and ${friendship.requestedId}`);

          // Send notification about accepted friend request
          await this.notificationService.notify(
            friendship.requesterId,
            NotificationType.SYSTEM,
            'Friend Request Accepted',
            `${requested.name} has accepted your friend request.`,
            {
              relatedEntityId: updatedFriendship.id,
              relatedEntityType: 'friendship',
              htmlContent: `<p><strong>${requested.name}</strong> has accepted your friend request. You can now chat with each other.</p>`,
              sendInApp: true,
              sendPush: true
            }
          );
        } catch (error) {
          // Log the error but don't fail the whole operation
          this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
        }
      } else if (friendship.status === FriendshipStatus.REJECTED) {
        // Send notification about rejected friend request
        await this.notificationService.notify(
          friendship.requesterId,
          NotificationType.SYSTEM,
          'Friend Request Rejected',
          `${requested.name} has rejected your friend request.`,
          {
            relatedEntityId: updatedFriendship.id,
            relatedEntityType: 'friendship',
            sendInApp: true
          }
        );
      }

      return this.mapFriendshipToResponseDto(updatedFriendship, requester, requested);
    } catch (error) {
      this.logger.error(`Error responding to friend request: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Accept or reject a diary follow request
   * @param requestId ID of the diary follow request
   * @param userId ID of the user responding to the request
   * @param respondToRequestDto Response data
   * @returns The updated diary follow request
   */
  async respondToDiaryFollowRequest(
    requestId: string,
    userId: string,
    respondToRequestDto: RespondToRequestDto
  ): Promise<DiaryFollowRequestResponseDto> {
    try {
      // Find the diary follow request
      const followRequest = await this.diaryFollowRequestRepository.findOne({
        where: { id: requestId, diaryOwnerId: userId, status: FollowRequestStatus.PENDING }
      });

      if (!followRequest) {
        throw new NotFoundException(`Diary follow request with ID ${requestId} not found`);
      }

      // Update the request status
      followRequest.status = respondToRequestDto.status === FriendshipStatus.ACCEPTED
        ? FollowRequestStatus.ACCEPTED
        : FollowRequestStatus.REJECTED;

      // Save the updated request
      const updatedRequest = await this.diaryFollowRequestRepository.save(followRequest);

      // If accepted, update the friendship to allow diary viewing
      if (respondToRequestDto.status === FriendshipStatus.ACCEPTED) {
        // Find the friendship
        let friendship = await this.studentFriendshipRepository.findOne({
          where: [
            { requesterId: followRequest.requesterId, requestedId: userId },
            { requesterId: userId, requestedId: followRequest.requesterId }
          ]
        });

        // If no friendship exists, create one
        if (!friendship) {
          friendship = this.studentFriendshipRepository.create({
            requesterId: followRequest.requesterId,
            requestedId: userId,
            status: FriendshipStatus.ACCEPTED,
            requestMessage: 'Automatically created from diary follow request',
            canViewDiary: true
          });
        } else {
          // Update the existing friendship
          friendship.status = FriendshipStatus.ACCEPTED;
          friendship.canViewDiary = true;
        }

        await this.studentFriendshipRepository.save(friendship);

        // Create a chat conversation between the users if one doesn't exist
        try {
          const conversation = await this.chatService.getOrCreateConversation(
            followRequest.requesterId,
            userId
          );

          this.logger.log(`Created or retrieved chat conversation ${conversation.id} between users ${followRequest.requesterId} and ${userId}`);
        } catch (error) {
          // Log the error but don't fail the whole operation
          this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
        }
      }

      // Get the users involved
      const requester = await this.userRepository.findOne({
        where: { id: followRequest.requesterId },
        select: ['id', 'name', 'profilePicture']
      });

      const diaryOwner = await this.userRepository.findOne({
        where: { id: followRequest.diaryOwnerId },
        select: ['id', 'name', 'profilePicture']
      });

      // Send notification to the requester
      if (followRequest.status === FollowRequestStatus.ACCEPTED) {
        await this.notificationService.notify(
          followRequest.requesterId,
          NotificationType.SYSTEM,
          'Diary Follow Request Accepted',
          `${diaryOwner.name} has accepted your request to follow their diary.`,
          {
            relatedEntityId: updatedRequest.id,
            relatedEntityType: 'diary_follow_request',
            htmlContent: `<p><strong>${diaryOwner.name}</strong> has accepted your request to follow their diary. You can now view their diary entries.</p>`,
            sendInApp: true,
            sendPush: true
          }
        );
      } else if (followRequest.status === FollowRequestStatus.REJECTED) {
        await this.notificationService.notify(
          followRequest.requesterId,
          NotificationType.SYSTEM,
          'Diary Follow Request Rejected',
          `${diaryOwner.name} has rejected your request to follow their diary.`,
          {
            relatedEntityId: updatedRequest.id,
            relatedEntityType: 'diary_follow_request',
            sendInApp: true
          }
        );
      }

      return this.mapDiaryFollowRequestToResponseDto(updatedRequest, requester, diaryOwner);
    } catch (error) {
      this.logger.error(`Error responding to diary follow request: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all friends for a student
   * @param userId ID of the student
   * @param paginationDto Pagination parameters
   * @returns Paged list of friends
   */
  async getFriends(
    userId: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<FriendshipResponseDto>> {
    try {
      // Default pagination
      const page = paginationDto?.page || 1;
      const limit = paginationDto?.limit || 10;
      const skip = (page - 1) * limit;

      // Get all accepted friendships where the user is either the requester or the requested
      const [friendships, total] = await this.studentFriendshipRepository.findAndCount({
        where: [
          { requesterId: userId, status: FriendshipStatus.ACCEPTED },
          { requestedId: userId, status: FriendshipStatus.ACCEPTED }
        ],
        skip,
        take: limit,
        order: { updatedAt: 'DESC' }
      });

      // Get all user IDs involved in the friendships
      const userIds = new Set<string>();
      friendships.forEach(friendship => {
        userIds.add(friendship.requesterId);
        userIds.add(friendship.requestedId);
      });

      // Get all users involved in the friendships
      const users = await this.userRepository.find({
        where: { id: In([...userIds]) },
        select: ['id', 'name']
      });

      // Map users to a dictionary for easy lookup
      const userMap = new Map<string, User>();
      users.forEach(user => userMap.set(user.id, user));

      // Map friendships to response DTOs from the current user's perspective
      const items = await Promise.all(friendships.map(async (friendship) => {
        const requester = userMap.get(friendship.requesterId);
        const requested = userMap.get(friendship.requestedId);
        return this.mapFriendshipToResponseDtoFromUserPerspective(friendship, requester, requested, userId);
      }));

      return new PagedListDto(
        items,
        total,
        page,
        limit
      );
    } catch (error) {
      this.logger.error(`Error getting friends: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all diary follows for a student
   * @param userId ID of the student
   * @param paginationDto Pagination parameters
   * @returns Paged list of diary follows
   */
  async getDiaryFollows(
    userId: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<DiaryFollowRequestResponseDto>> {
    try {
      // Default pagination
      const page = paginationDto?.page || 1;
      const limit = paginationDto?.limit || 10;
      const skip = (page - 1) * limit;

      // Get explicit diary follow requests
      const explicitFollows = await this.diaryFollowRequestRepository.find({
        where: { requesterId: userId },
        order: { updatedAt: 'DESC' }
      });

      // Get friendships with diary access
      const friendships = await this.studentFriendshipRepository.find({
        where: [
          { requesterId: userId, status: FriendshipStatus.ACCEPTED, canViewDiary: true },
          { requestedId: userId, status: FriendshipStatus.ACCEPTED, canViewDiary: true }
        ],
        relations: ['requester', 'requested'],
        order: { updatedAt: 'DESC' }
      });

      // Convert friendships to follow requests format, but only if no explicit request exists
      const friendshipFollows = friendships
        .filter(friendship => !explicitFollows.some(follow =>
          follow.diaryOwnerId === (friendship.requesterId === userId ? friendship.requestedId : friendship.requesterId)
        ))
        .map(friendship => {
          const isRequester = friendship.requesterId === userId;
          const follower = isRequester ? friendship.requester : friendship.requested;
          const followed = isRequester ? friendship.requested : friendship.requester;

          return this.diaryFollowRequestRepository.create({
            id: `friendship-${friendship.id}`,
            requesterId: follower.id,
            diaryOwnerId: followed.id,
            friendshipId: friendship.id,
            status: FollowRequestStatus.ACCEPTED,
            createdAt: friendship.createdAt,
            updatedAt: friendship.updatedAt
          });
        });

      // Combine both types of follows
      const allFollows = [...explicitFollows, ...friendshipFollows];

      // Apply pagination
      const total = allFollows.length;
      const follows = allFollows.slice(skip, skip + limit);

      // Get all user IDs involved in the follow requests
      const userIds = new Set<string>();
      follows.forEach(request => {
        userIds.add(request.requesterId);
        userIds.add(request.diaryOwnerId);
      });

      // Get all users involved in the follow requests
      const users = await this.userRepository.find({
        where: { id: In([...userIds]) },
        select: ['id', 'name', 'profilePicture']
      });

      // Map users to a dictionary for easy lookup
      const userMap = new Map<string, User>();
      users.forEach(user => userMap.set(user.id, user));

      // Map follow requests to response DTOs
      const items = await Promise.all(follows.map(async (request) => {
        const requester = userMap.get(request.requesterId);
        const diaryOwner = userMap.get(request.diaryOwnerId);
        return this.mapDiaryFollowRequestToResponseDto(request, requester, diaryOwner);
      }));

      return new PagedListDto(
        items,
        total,
        page,
        limit
      );
    } catch (error) {
      this.logger.error(`Error getting diary follows: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a student can view another student's diary
   * @param viewerId ID of the student trying to view the diary
   * @param ownerId ID of the diary owner
   * @returns Whether the viewer can view the diary
   */
  async canViewDiary(viewerId: string, ownerId: string): Promise<boolean> {
    try {
      // If the viewer is the owner, they can view the diary
      if (viewerId === ownerId) {
        return true;
      }

      // Check if there's a friendship with canViewDiary=true
      const friendship = await this.studentFriendshipRepository.findOne({
        where: [
          { requesterId: viewerId, requestedId: ownerId, status: FriendshipStatus.ACCEPTED, canViewDiary: true },
          { requesterId: ownerId, requestedId: viewerId, status: FriendshipStatus.ACCEPTED, canViewDiary: true }
        ]
      });

      return !!friendship;
    } catch (error) {
      this.logger.error(`Error checking if student can view diary: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Map a friendship entity to a response DTO
   * @param friendship The friendship entity
   * @param requester The requester user
   * @param requested The requested user
   * @returns The friendship response DTO
   */
  private async mapFriendshipToResponseDto(
    friendship: StudentFriendship,
    requester: User,
    requested: User
  ): Promise<FriendshipResponseDto> {
    // Get profile picture URLs using ProfilePictureService
    let requesterProfilePicture = null;
    if (requester?.id) {
      requesterProfilePicture = await this.profilePictureService.getProfilePictureUrl(requester.id);
    }

    let requestedProfilePicture = null;
    if (requested?.id) {
      requestedProfilePicture = await this.profilePictureService.getProfilePictureUrl(requested.id);
    }

    // Get or create conversation between the users if friendship is accepted
    let conversationId = null;
    if (friendship.status === FriendshipStatus.ACCEPTED && requester?.id && requested?.id) {
      try {
        const conversation = await this.chatService.getOrCreateConversation(
          requester.id,
          requested.id
        );
        conversationId = conversation.id;
        this.logger.log(`Retrieved conversation ${conversation.id} between users ${requester.id} and ${requested.id}`);
      } catch (error) {
        // Log the error but don't fail the whole operation
        this.logger.error(`Error getting conversation for friendship ${friendship.id}: ${error.message}`, error.stack);
      }
    }

    return {
      id: friendship.id,
      requesterId: friendship.requesterId,
      requesterName: requester?.name || 'Unknown',
      requesterProfilePicture,
      requestedId: friendship.requestedId,
      requestedName: requested?.name || 'Unknown',
      requestedProfilePicture,
      status: friendship.status,
      requestMessage: friendship.requestMessage,
      canViewDiary: friendship.canViewDiary,
      createdAt: friendship.createdAt,
      // Default friend fields (will show requester as friend for backward compatibility)
      friendId: requester?.id || '',
      friendName: requester?.name || 'Unknown',
      friendProfilePicture: requesterProfilePicture,
      conversationId
    };
  }

  /**
   * Map a friendship entity to a response DTO from the current user's perspective
   * This method ensures that the friend's information is consistently shown regardless of who sent the original request
   * @param friendship The friendship entity
   * @param requester The requester user
   * @param requested The requested user
   * @param currentUserId The ID of the current user viewing their friends
   * @returns The friendship response DTO from the current user's perspective
   */
  private async mapFriendshipToResponseDtoFromUserPerspective(
    friendship: StudentFriendship,
    requester: User,
    requested: User,
    currentUserId: string
  ): Promise<FriendshipResponseDto> {
    // Determine who is the friend from the current user's perspective
    const isCurrentUserRequester = friendship.requesterId === currentUserId;
    const friend = isCurrentUserRequester ? requested : requester;
    const currentUser = isCurrentUserRequester ? requester : requested;

    // Get profile picture URLs using ProfilePictureService
    let requesterProfilePicture = null;
    if (requester?.id) {
      requesterProfilePicture = await this.profilePictureService.getProfilePictureUrl(requester.id);
    }

    let requestedProfilePicture = null;
    if (requested?.id) {
      requestedProfilePicture = await this.profilePictureService.getProfilePictureUrl(requested.id);
    }

    // Get friend's profile picture
    let friendProfilePicture = null;
    if (friend?.id) {
      friendProfilePicture = await this.profilePictureService.getProfilePictureUrl(friend.id);
    }

    // Get or create conversation between the current user and their friend if friendship is accepted
    let conversationId = null;
    if (friendship.status === FriendshipStatus.ACCEPTED && friend?.id) {
      try {
        const conversation = await this.chatService.getOrCreateConversation(
          currentUserId,
          friend.id
        );
        conversationId = conversation.id;
        this.logger.log(`Retrieved conversation ${conversation.id} between users ${currentUserId} and ${friend.id}`);
      } catch (error) {
        // Log the error but don't fail the whole operation
        this.logger.error(`Error getting conversation for friendship ${friendship.id}: ${error.message}`, error.stack);
      }
    }

    // Return the response with both original structure and friend-specific fields
    return {
      id: friendship.id,
      requesterId: friendship.requesterId,
      requesterName: requester?.name || 'Unknown',
      requesterProfilePicture,
      requestedId: friendship.requestedId,
      requestedName: requested?.name || 'Unknown',
      requestedProfilePicture,
      status: friendship.status,
      requestMessage: friendship.requestMessage,
      canViewDiary: friendship.canViewDiary,
      createdAt: friendship.createdAt,
      // Friend-specific fields for easier client-side handling
      friendId: friend?.id || '',
      friendName: friend?.name || 'Unknown',
      friendProfilePicture,
      conversationId
    };
  }

  /**
   * Map a diary follow request entity to a response DTO
   * @param request The diary follow request entity
   * @param requester The requester user
   * @param diaryOwner The diary owner user
   * @returns The diary follow request response DTO
   */
  private async mapDiaryFollowRequestToResponseDto(
    request: DiaryFollowRequest,
    requester: User,
    diaryOwner: User
  ): Promise<DiaryFollowRequestResponseDto> {
    // Get profile picture URLs using ProfilePictureService
    let requesterProfilePicture = null;
    if (requester?.id) {
      requesterProfilePicture = await this.profilePictureService.getProfilePictureUrl(requester.id);
    }

    let diaryOwnerProfilePicture = null;
    if (diaryOwner?.id) {
      diaryOwnerProfilePicture = await this.profilePictureService.getProfilePictureUrl(diaryOwner.id);
    }

    return {
      id: request.id,
      requesterId: request.requesterId,
      requesterName: requester?.name || 'Unknown',
      requesterProfilePicture,
      diaryOwnerId: request.diaryOwnerId,
      diaryOwnerName: diaryOwner?.name || 'Unknown',
      diaryOwnerProfilePicture,
      status: request.status,
      requestMessage: request.requestMessage,
      createdAt: request.createdAt
    };
  }

  /**
   * Get or create conversation with a friend
   * @param userId Current user ID
   * @param friendId Friend user ID
   * @returns Conversation data with friend information
   */
  async getConversationWithFriend(
    userId: string,
    friendId: string
  ): Promise<ConversationByFriendResponseDto> {
    try {
      // Validate that both users exist and are students
      const [currentUser, friend] = await Promise.all([
        this.userRepository.findOne({
          where: { id: userId, type: UserType.STUDENT },
          select: ['id', 'name', 'profilePicture']
        }),
        this.userRepository.findOne({
          where: { id: friendId, type: UserType.STUDENT },
          select: ['id', 'name', 'profilePicture']
        })
      ]);

      if (!currentUser) {
        throw new NotFoundException('Current user not found');
      }

      if (!friend) {
        throw new NotFoundException('Friend not found');
      }

      // Check if they are friends (accepted friendship)
      const friendship = await this.studentFriendshipRepository.findOne({
        where: [
          { requesterId: userId, requestedId: friendId, status: FriendshipStatus.ACCEPTED },
          { requesterId: friendId, requestedId: userId, status: FriendshipStatus.ACCEPTED }
        ]
      });

      if (!friendship) {
        throw new NotFoundException('Users are not friends or friendship is not accepted');
      }

      // Get friend's profile picture
      let friendProfilePicture = null;
      if (friend.id) {
        friendProfilePicture = await this.profilePictureService.getProfilePictureUrl(friend.id);
      }

      // Check if conversation already exists
      let conversation;
      let isNewConversation = false;

      try {
        // First check if conversation already exists
        const existingConversation = await this.conversationRepository.findOne({
          where: [
            { participant1Id: userId, participant2Id: friendId },
            { participant1Id: friendId, participant2Id: userId }
          ]
        });

        if (existingConversation) {
          conversation = existingConversation;
          isNewConversation = false;
          this.logger.log(`Found existing conversation ${conversation.id} between users ${userId} and ${friendId}`);
        } else {
          // Create new conversation
          conversation = await this.chatService.getOrCreateConversation(userId, friendId);
          isNewConversation = true;
          this.logger.log(`Created new conversation ${conversation.id} between users ${userId} and ${friendId}`);
        }
      } catch (error) {
        this.logger.error(`Error getting conversation between users ${userId} and ${friendId}: ${error.message}`, error.stack);
        throw new Error('Unable to create or retrieve conversation. Users may not be allowed to chat.');
      }

      return {
        conversationId: conversation.id,
        friendId: friend.id,
        friendName: friend.name,
        friendProfilePicture,
        isNewConversation
      };
    } catch (error) {
      this.logger.error(`Error getting conversation with friend: ${error.message}`, error.stack);
      throw error;
    }
  }
}

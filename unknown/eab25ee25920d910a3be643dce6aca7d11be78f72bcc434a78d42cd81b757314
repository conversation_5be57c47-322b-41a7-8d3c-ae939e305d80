import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { EssayTaskSubmissionMarking } from "src/database/entities/essay-task-submission-marking.entity";
import { EssayTaskSubmissions, SubmissionStatus } from "src/database/entities/essay-task-submissions.entity";
import { EssayTaskSubmissionHistory } from "src/database/entities/essay-task-submission-history.entity";
import { EssayTaskSubmissionMarkingDto, CreateEssayTaskSubmissionMarkingDto } from 'src/database/models/mission.dto';
import { NotificationHelperService } from '../../modules/notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';

@Injectable()
export class TutorEssayService {
  private readonly logger = new Logger(TutorEssayService.name);

  constructor(
    @InjectRepository(EssayTaskSubmissions)
    private readonly essayTaskSubmissionsRepository: Repository<EssayTaskSubmissions>,
    @InjectRepository(EssayTaskSubmissionHistory)
    private readonly essayTaskSubmissionHistoryRepository: Repository<EssayTaskSubmissionHistory>,
    @InjectRepository(EssayTaskSubmissionMarking)
    private readonly essayTaskSubmissionMarkingRepository: Repository<EssayTaskSubmissionMarking>,
    private dataSource: DataSource,
    private readonly notificationHelper: NotificationHelperService,
    private readonly deeplinkService: DeeplinkService
  ) {}

  private toMissionTaskSubmissionMarkingDto(taskSubmissionMark: EssayTaskSubmissionMarking): EssayTaskSubmissionMarkingDto {
    return {
      id: taskSubmissionMark.id,
      submissionId: taskSubmissionMark.submissionId,
      submissionHistoryId: taskSubmissionMark.submissionHistoryId,
      submissionFeedback: taskSubmissionMark.submissionFeedback,
      points: taskSubmissionMark.points,
      taskRemarks: taskSubmissionMark.taskRemarks,
    };
  }

  async markEssayTaskSubmission(
    essaySubmissionMarkDto: CreateEssayTaskSubmissionMarkingDto
  ): Promise<EssayTaskSubmissionMarkingDto> {
    const { submissionId } = essaySubmissionMarkDto;

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const submissionRepo = queryRunner.manager.getRepository(EssayTaskSubmissions);
      const historyRepo = queryRunner.manager.getRepository(EssayTaskSubmissionHistory);
      const markingRepo = queryRunner.manager.getRepository(EssayTaskSubmissionMarking);

      const taskSubmission = await submissionRepo.findOne({
        where: {
          id: submissionId,
          status: SubmissionStatus.SUBMITTED,
        },
      });

      if (!taskSubmission) {
        throw new NotFoundException('Task submission not found');
      }

      const taskSubmissionHistory = await historyRepo.findOne({
        where: {
          id: taskSubmission.latestSubmissionId,
          submissionId: submissionId,
        },
      });

      if (!taskSubmissionHistory) {
        throw new NotFoundException('Task submission history not found');
      }

      const existingMark = await markingRepo.findOne({
        where: {
          submissionId: submissionId,
          submissionHistoryId: taskSubmissionHistory.id,
        },
      });

      if (existingMark) {
        throw new BadRequestException('Task submission mark already exists');
      }

      const newTaskMark = markingRepo.create({
        ...essaySubmissionMarkDto,
        submissionHistoryId: taskSubmissionHistory.id,
      });

      await markingRepo.save(newTaskMark);

      taskSubmission.status = SubmissionStatus.REVIEWED;
      taskSubmission.currentRevision += 1;
      taskSubmission.isActive = false;
      taskSubmission.submissionMark = newTaskMark;

      taskSubmissionHistory.submissionMark = newTaskMark;

      await submissionRepo.save(taskSubmission);
      await historyRepo.save(taskSubmissionHistory);

      await queryRunner.commitTransaction();

      // Send notification to the student
      try {
        // Get the student ID from the submission
        const studentId = taskSubmission.createdBy;

        if (studentId) {
          // Generate deeplinks
          const webLink = this.deeplinkService.getWebLink(DeeplinkType.ESSAY_REVIEW, {
            id: submissionId
          });

          const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.ESSAY_REVIEW, {
            id: submissionId
          });

          // Create HTML content for rich notifications
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">Essay Submission Reviewed</h2>
              <p>Your essay submission has been reviewed by your tutor.</p>
              <p><strong>Score:</strong> ${essaySubmissionMarkDto.points}</p>
              <div style="margin: 20px 0;">
                <a href="${webLink}" style="background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; display: inline-block;">View Review</a>
              </div>
            </div>
          `;

          // Send notification
          await this.notificationHelper.notify(
            studentId,
            NotificationType.ESSAY_REVIEW,
            'Essay Submission Reviewed',
            `Your essay submission has been reviewed by your tutor.`,
            {
              relatedEntityId: submissionId,
              relatedEntityType: 'essay_submission',
              htmlContent: htmlContent,
              webLink: webLink,
              deepLink: deepLink,
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendMobile: true,
              sendSms: false,
              sendRealtime: false
            }
          );

          this.logger.log(`Sent notification to student ${studentId} for essay submission ${submissionId}`);
        }
      } catch (notificationError) {
        // Log the error but don't fail the marking process
        this.logger.error(`Error sending notification: ${notificationError.message}`, notificationError.stack);
      }

      return this.toMissionTaskSubmissionMarkingDto(newTaskMark);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
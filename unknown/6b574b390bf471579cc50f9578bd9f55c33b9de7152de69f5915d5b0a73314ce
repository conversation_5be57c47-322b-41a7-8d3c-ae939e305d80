import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryMission } from './diary-mission.entity';
import { User } from './user.entity';
import { MissionDiaryEntryFeedback } from './mission-diary-entry-feedback.entity';

/**
 * Status of a mission diary entry in its lifecycle
 * @enum {string}
 */
export enum MissionEntryStatus {
  /** Entry is still being edited by the student */
  NEW = 'NEW',
  /** Entry has been submitted for review */
  SUBMITTED = 'SUBMITTED',
  /** Entry has been reviewed by a tutor but not yet confirmed */
  REVIEWED = 'REVIEWED',
  /** Entry has been confirmed by tutor and review is completed */
  CONFIRMED = 'CONFIRMED',

  // Legacy values for backward compatibility
  /** @deprecated Use NEW instead */
  LEGACY_NEW = 'new',
  /** @deprecated Use SUBMITTED instead */
  LEGACY_SUBMIT = 'submit',
  /** @deprecated Use REVIEWED instead */
  LEGACY_REVIEWED = 'reviewed',
  /** @deprecated Use CONFIRMED instead */
  LEGACY_CONFIRM = 'confirm'
}

@Entity()
export class MissionDiaryEntry extends AuditableBaseEntity {
  @Column({ name: 'mission_id', type: 'uuid' })
  missionId: string;

  @ManyToOne(() => DiaryMission, mission => mission.entries)
  @JoinColumn({ name: 'mission_id' })
  mission: DiaryMission;

  @Column({ name: 'student_id', type: 'uuid' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'word_count', type: 'integer' })
  wordCount: number;

  @Column({ name: 'progress', type: 'float' })
  progress: number;

  @Column({
    name: 'status',
    type: 'enum',
    enum: MissionEntryStatus,
    default: MissionEntryStatus.NEW
  })
  status: MissionEntryStatus;

  @Column({ name: 'gained_score', type: 'integer', nullable: true })
  gainedScore: number;

  @Column({ name: 'reviewed_by', type: 'uuid', nullable: true })
  reviewedBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'reviewed_by' })
  reviewer: User;

  @Column({ name: 'reviewed_at', type: 'timestamp', nullable: true })
  reviewedAt: Date;

  @Column({ name: 'correction', type: 'text', nullable: true })
  correction: string;

  @Column({ name: 'correction_provided_at', type: 'timestamp', nullable: true })
  correctionProvidedAt: Date;

  @Column({ name: 'correction_provided_by', type: 'uuid', nullable: true })
  correctionProvidedBy: string;

  @OneToMany(() => MissionDiaryEntryFeedback, feedback => feedback.missionEntry)
  feedbacks: MissionDiaryEntryFeedback[];
}
